import { draw } from "@/plugins/svgjs";
import { store } from "./store";
import {
  addSendDataActionToSequence,
  cleanEmptyFrames,
  createSequence,
  deleteSequenceFrameAction,
  ELEMENT_POSITION,
  handleSequenceActionsForMovedElement,
  IMPORTED_SEQUENCE,
  repairSequence,
  selectSequence,
  updateInitialSequencesAfterElementUpdateInCreationMode,
} from "@/sequence";
import {
  resetFileHandle,
  saveDefault,
  saveFile,
  saveTypes,
  setSaveDafault,
  updateFile,
} from "@/files";
import { drawAdapter } from "@/draw-adapter";
import {
  forceStopAnimation,
  immersiveViewOpacity,
  playFrame,
  redrawElementsAtFrame,
  setupElementsForCreation,
  setupObjectsAtFrame,
  stopSequence,
  zoomToElement,
} from "@/animation";

const { v4: uuidv4 } = require("uuid");

export const diagramMode = {
  creationMode: "CREATION_MODE",
  playbackMode: "PLAYBACK_MODE",
  recordingMode: "RECORDING_MODE",
};

export const selectionMode = {
  pointer: "POINTER",
  grabber: "GRABBER",
};

let mediaRecorder;

export async function initialise() {
  drawAdapter.initialise();
  await store.dispatch("initializeRootWorkspaceAction");
}

export const setup = () => {
  const viewboxX = 2500;
  const viewboxY = 2500;
  const viewboxWidth = 10000;
  const viewboxHeight = 10000;
  drawAdapter.setup(viewboxX, viewboxY, viewboxWidth, viewboxHeight);
};

export function changeBackground(
  color,
  colorSmallSquares,
  colorBigSquares,
  blank,
) {
  if (!store.state.displayGrid) {
    blank = true;
  }
  drawAdapter.changeBackground(
    color,
    colorSmallSquares,
    colorBigSquares,
    blank,
  );
}

export const setRecordingBackground = () => {
  changeBackground();
};

export const setNormalBackground = () => {
  changeBackground();
};

export const createNewElementAtPosition = async (
  posX,
  posY,
  type,
  elementName,
  elementNameX,
  elementNameY,
  elementId,
  elementColor,
  textColor,
  textSize,
  textFont,
  textWeight,
  textAlign,
  width,
  height,
  anchors,
  undoable,
) => {
  type = type || store.state.defaultShape;
  const imported = !!elementId;
  if (!elementNameX) {
    elementName =
      elementName ||
      (type === "TEXT-CLASSIC"
        ? "Add your text here \n please"
        : "Element " + store.state.allSimpleElements.size);
  }
  width = width || (type === "TEXT-CLASSIC" ? 200 : 100);
  height = height || (type === "TEXT-CLASSIC" ? 50 : 100);
  textColor = textColor || (type === "TEXT-CLASSIC" ? "#000000" : "#ffffff");
  textSize = textSize || 16;
  textFont = textFont || "Arial";
  textWeight = textWeight || "bold";
  textAlign = textAlign || "center";
  anchors = anchors || createAnchors(posX, posY, width, height);
  // Store element name position relative to element center
  const relativeTextX = elementNameX ? elementNameX - posX : -width / 2 + 10;
  const relativeTextY = elementNameY ? elementNameY - posY : 0;

  const displayShadows = store.state.displayShadows;

  // Create a workspace parameters map with the initial position and anchors for the current workspace
  const workspaceParameters = {};
  workspaceParameters[store.state.currentWorkspaceId] = {
    x: posX,
    y: posY,
    width,
    height,
    anchors: anchors,
    textX: relativeTextX,
    textY: relativeTextY,
    destinationElementIds: [],
  };

  const element = {
    id: elementId || uuidv4(),
    name: elementName,
    type: type,
    destinationElementIds: [],
    color:
      elementColor ||
      (type === "TEXT-CLASSIC" ? "#ffffff00" : getRandomColor()),
    workspaceParameters: workspaceParameters,
    textColor: textColor,
    textSize: textSize,
    textFont: textFont,
    textWeight: textWeight,
    textAlign: textAlign,
    parentWorkspaceIds: [store.state.currentWorkspaceId],
    childWorkspaceId: null,
  };
  // Calculate absolute positions for drawing
  const absoluteAnchors = getAbsoluteAnchors(anchors, posX, posY);
  const absoluteTextX = posX + relativeTextX;
  const absoluteTextY = posY + relativeTextY;

  drawAdapter.createElement(
    posX,
    posY,
    type,
    elementName,
    absoluteTextX,
    absoluteTextY,
    element.id,
    element.color,
    textColor,
    textSize,
    textFont,
    textWeight,
    textAlign,
    width,
    height,
    absoluteAnchors,
    displayShadows,
  );

  await updateInitialSequencesAfterElementUpdateInCreationMode({
    element,
    undoable: true,
  });

  await store.dispatch("updateSimpleElement", {
    simpleElement: element,
    undoable: true,
  });

  if (!imported) {
    await addElementToSelection({
      elementId: element.id,
      deselectOthers: true,
      undoable,
    });
  }

  snapElementToGrid(element.id, undoable);

  return element;
};

export function createElementFromState(elementId, workspaceId, displayShadows) {
  const simpleElement = store.state.allSimpleElements.get(elementId);
  const workspaceParams = simpleElement.workspaceParameters[workspaceId];

  // Calculate absolute positions for drawing
  const absoluteAnchors = getAbsoluteAnchors(
    workspaceParams.anchors,
    workspaceParams.x,
    workspaceParams.y,
  );
  const absoluteTextX = workspaceParams.x + workspaceParams.textX;
  const absoluteTextY = workspaceParams.y + workspaceParams.textY;

  return drawAdapter.createElement(
    workspaceParams.x,
    workspaceParams.y,
    simpleElement.type,
    simpleElement.name,
    absoluteTextX,
    absoluteTextY,
    simpleElement.id,
    simpleElement.color,
    simpleElement.textColor,
    simpleElement.textSize,
    simpleElement.textFont,
    simpleElement.textWeight,
    simpleElement.textAlign,
    workspaceParams.width,
    workspaceParams.height,
    absoluteAnchors,
    displayShadows,
  );
}

export const createAnchors = (x, y, width, height) => {
  // Return relative positions from element center
  const anchors = {};
  anchors.top = {
    x: 0,
    y: -height / 2,
  };
  anchors.bottom = {
    x: 0,
    y: height / 2,
  };
  anchors.left = {
    x: -width / 2,
    y: 0,
  };
  anchors.right = {
    x: width / 2,
    y: 0,
  };
  return anchors;
};

/**
 * Calculate absolute anchor positions from relative anchors and element position
 * @param {Object} relativeAnchors - Anchors with relative positions
 * @param {number} elementX - Element center X coordinate
 * @param {number} elementY - Element center Y coordinate
 * @returns {Object} Anchors with absolute positions
 */
export const getAbsoluteAnchors = (relativeAnchors, elementX, elementY) => {
  const absoluteAnchors = {};
  for (const [key, anchor] of Object.entries(relativeAnchors)) {
    absoluteAnchors[key] = {
      x: elementX + anchor.x,
      y: elementY + anchor.y,
    };
  }
  return absoluteAnchors;
};

/**
 * Check if a workspace element is expanded
 * @param {string} elementId - The element ID
 * @returns {boolean} True if the workspace is expanded
 */
export const isWorkspaceExpanded = (elementId) => {
  const element = store.state.allSimpleElements.get(elementId);
  if (!element || !element.childWorkspaceId) return false;

  const workspaceParams =
    element.workspaceParameters[store.state.currentWorkspaceId];
  return workspaceParams && workspaceParams.expandedParameters;
};

/**
 * Get expanded workspace parameters
 * @param {string} elementId - The element ID
 * @returns {Object|null} Expanded parameters or null if not expanded
 */
export const getExpandedWorkspaceParameters = (elementId) => {
  const element = store.state.allSimpleElements.get(elementId);
  if (!element || !element.childWorkspaceId) return null;

  const workspaceParams =
    element.workspaceParameters[store.state.currentWorkspaceId];
  return workspaceParams?.expandedParameters || null;
};

/**
 * Updates an expanded workspace element's position in state based on its graphic position
 * @param {string} elementId - The ID of the element containing the expanded workspace
 * @param {number} newX - The new x coordinate
 * @param {number} newY - The new y coordinate
 * @param {boolean} undoable - Whether this action should be undoable
 * @returns {Promise<void>}
 */
export const updateExpandedWorkspaceElementPosition = async (
  elementId,
  newX,
  newY,
  undoable = false,
) => {
  const element = store.state.allSimpleElements.get(elementId);
  if (!element) {
    console.error(`Element with ID ${elementId} not found`);
    return;
  }

  // Update the element's position in state
  const updatedElement = deepClone(element);
  updatedElement.workspaceParameters[store.state.currentWorkspaceId].x = newX;
  updatedElement.workspaceParameters[store.state.currentWorkspaceId].y = newY;

  await store.dispatch("updateSimpleElement", {
    simpleElement: updatedElement,
    undoable,
  });
};

const getRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

export const configureElementAnchors = (elementId) => {
  const element = store.state.allSimpleElements.get(elementId);
  const workspaceParams =
    element.workspaceParameters[store.state.currentWorkspaceId];

  // Create relative anchors if they don't exist
  if (!workspaceParams.anchors) {
    workspaceParams.anchors = createAnchors(
      workspaceParams.x,
      workspaceParams.y,
      workspaceParams.width,
      workspaceParams.height,
    );
  }

  // Calculate absolute positions for drawing
  const absoluteAnchors = getAbsoluteAnchors(
    workspaceParams.anchors,
    workspaceParams.x,
    workspaceParams.y,
  );

  drawAdapter.configureElementAnchors(elementId, absoluteAnchors);

  store.dispatch("updateSimpleElement", {
    simpleElement: element,
    undoable: false,
  });
};

export const updateElementDimensionsFromGraphic = (simpleElement) => {
  const graphicElementProperties = drawAdapter.getGraphicElementProperties(
    simpleElement.id,
  );
  simpleElement.workspaceParameters[store.state.currentWorkspaceId].width =
    graphicElementProperties.width;
  simpleElement.workspaceParameters[store.state.currentWorkspaceId].height =
    graphicElementProperties.height;
  simpleElement.workspaceParameters[store.state.currentWorkspaceId].x =
    graphicElementProperties.x;
  simpleElement.workspaceParameters[store.state.currentWorkspaceId].y =
    graphicElementProperties.y;

  store.dispatch("updateSimpleElement", {
    simpleElement: simpleElement,
    undoable: true,
  });
};

export function snapElementToGrid(elementId, undoable) {
  if (!store.state.snapToGrid) {
    return;
  }
  const element = store.state.allSimpleElements.get(elementId);
  // If the element goes more than half of a grid unit, stick to the next grid unit.
  // Otherwise stick to previous grid unit
  // Coordinates of top left corner

  // Get position from workspaceParameters if available
  let elementX, elementY;
  elementX = element.workspaceParameters[store.state.currentWorkspaceId].x;
  elementY = element.workspaceParameters[store.state.currentWorkspaceId].y;

  const x =
    elementX -
    element.workspaceParameters[store.state.currentWorkspaceId].width / 2;
  const y =
    elementY -
    element.workspaceParameters[store.state.currentWorkspaceId].height / 2;
  let newX, newY;
  const gridUnit = 10;

  if (x % gridUnit < gridUnit / 2) {
    newX = x - (x % gridUnit);
  } else {
    newX = x - (x % gridUnit) + gridUnit;
  }
  if (y % gridUnit < gridUnit / 2) {
    newY = y - (y % gridUnit);
  } else {
    newY = y - (y % gridUnit) + gridUnit;
  }
  // Update the default position
  element.workspaceParameters[store.state.currentWorkspaceId].x =
    newX +
    element.workspaceParameters[store.state.currentWorkspaceId].width / 2;
  element.workspaceParameters[store.state.currentWorkspaceId].y =
    newY +
    element.workspaceParameters[store.state.currentWorkspaceId].height / 2;

  drawAdapter.setPosition(
    element.id,
    element.workspaceParameters[store.state.currentWorkspaceId].x,
    element.workspaceParameters[store.state.currentWorkspaceId].y,
  );
  // Anchors are now relative and don't need manual updates
  retraceAllSegments(element.id);

  store
    .dispatch("updateSimpleElement", { simpleElement: element, undoable })
    .then();
}

export const createConnectionBetweenElements = (
  element1Id,
  element2Id,
  undoable,
) => {
  const element1 = store.state.allSimpleElements.get(element1Id);
  const element2 = store.state.allSimpleElements.get(element2Id);
  if (
    !(
      element1.workspaceParameters[
        store.state.currentWorkspaceId
      ].destinationElementIds.includes(element2.id) &&
      element2.workspaceParameters[
        store.state.currentWorkspaceId
      ].destinationElementIds.includes(element1.id)
    )
  ) {
    element1.workspaceParameters[
      store.state.currentWorkspaceId
    ].destinationElementIds.push(element2.id);
    element2.workspaceParameters[
      store.state.currentWorkspaceId
    ].destinationElementIds.push(element1.id);
    store
      .dispatch("updateSimpleElement", {
        simpleElement: element1,
        undoable: true,
      })
      .then();
    store
      .dispatch("updateSimpleElement", {
        simpleElement: element2,
        undoable: true,
      })
      .then();
    traceSegment(element1Id, element2Id, undoable);
  }
};

export const retraceAllSegments = (
  elementId,
  frameIndex,
  expandedWorkspaceId,
  offsetPosition,
) => {
  console.log("retraceAllSegments", elementId, expandedWorkspaceId, frameIndex);
  const sourceElement =
    elementId === null ? null : store.state.allSimpleElements.get(elementId);
  const workspaceIds = getEffectiveWorkspaceIds(elementId, frameIndex);
  workspaceIds.forEach((workspaceId) => {
    segmentIdsAvailable(frameIndex)
      .map((segmentId) => store.state.allSimpleSegments.get(segmentId))
      .filter(
        (segment) =>
          !sourceElement ||
          (sourceElement.workspaceParameters[
            workspaceId
          ]?.destinationElementIds.includes(
            segment.segmentElementsInfo.element1,
          ) &&
            sourceElement.id === segment.segmentElementsInfo.element2) ||
          (sourceElement.workspaceParameters[
            workspaceId
          ]?.destinationElementIds.includes(
            segment.segmentElementsInfo.element2,
          ) &&
            sourceElement.id === segment.segmentElementsInfo.element1),
      )
      .forEach((segment) => {
        traceSegment(
          segment.segmentElementsInfo.element1,
          segment.segmentElementsInfo.element2,
          true,
          frameIndex,
          expandedWorkspaceId,
          offsetPosition,
        );
      });
  });
};

export const traceSegment = (
  sourceElementId,
  destinationElementId,
  undoable,
  frameIndex,
  expandedWorkspaceId,
  offsetPosition,
) => {
  const sourceElement = store.state.allSimpleElements.get(sourceElementId);
  const destinationElement =
    store.state.allSimpleElements.get(destinationElementId);
  const workspaceIds = getEffectiveWorkspaceIds(sourceElementId, frameIndex);
  workspaceIds.forEach((workspaceId) => {
    let connectedAnchors = findConnectedAnchors(
      sourceElementId,
      destinationElementId,
      workspaceId,
    );
    if (connectedAnchors) {
      const foundSegmentEntry = Array.from(store.state.allSimpleSegments).find(
        ([key, value]) =>
          (value.segmentElementsInfo.element1 === sourceElement.id &&
            value.segmentElementsInfo.element2 === destinationElement.id) ||
          (value.segmentElementsInfo.element1 === destinationElement.id &&
            value.segmentElementsInfo.element2 === sourceElement.id &&
            key !== null),
      );
      const foundSegmentKey = foundSegmentEntry ? foundSegmentEntry[0] : null;
      const foundSegmentValue = foundSegmentEntry ? foundSegmentEntry[1] : null;

      const simpleSegment = {
        id: foundSegmentValue ? foundSegmentValue.id : uuidv4(),
        segmentElementsInfo: {
          element1: sourceElement.id,
          element2: destinationElement.id,
          mode: foundSegmentValue
            ? foundSegmentValue.segmentElementsInfo.mode
            : "LINEAR",
        },
        parentWorkspaceId: [store.state.currentWorkspaceId],
      };

      if (!foundSegmentKey) {
        store
          .dispatch("createSegment", { segment: simpleSegment, undoable })
          .then();
      }
      if (
        sourceElement.parentWorkspaceIds.includes(expandedWorkspaceId) &&
        destinationElement.parentWorkspaceIds.includes(expandedWorkspaceId)
      ) {
        drawAdapter.traceSegment(
          simpleSegment.id,
          simpleSegment.segmentElementsInfo.mode,
          connectedAnchors,
          expandedWorkspaceId,
          offsetPosition,
        );
      } else {
        console.log(
          "traceSegment in the else",
          sourceElement.parentWorkspaceIds,
          destinationElement.parentWorkspaceIds,
          expandedWorkspaceId,
        );
        // drawAdapter.traceSegment(
        //   simpleSegment.id,
        //   simpleSegment.segmentElementsInfo.mode,
        //   connectedAnchors,
        // );
      }
    }
  });
};

export const findConnectedAnchors = (
  sourceElementId,
  destinationElementId,
  workspaceId,
) => {
  const workspaceIds = [
    workspaceId,
    ...getEffectiveWorkspaceIds(sourceElementId),
  ];

  // Check if either element is an expanded workspace box
  const isSourceExpanded = isWorkspaceExpanded(sourceElementId);
  const isDestExpanded = isWorkspaceExpanded(destinationElementId);

  for (const workspaceId1 of workspaceIds) {
    let sourceAnchors, destAnchors;

    // Handle source element anchors
    if (isSourceExpanded) {
      // Get anchors from the expanded workspace parameters
      const expandedParams = getExpandedWorkspaceParameters(sourceElementId);
      const sourceElement = store.state.allSimpleElements.get(sourceElementId);
      if (expandedParams && sourceElement) {
        const workspaceParams = sourceElement.workspaceParameters[workspaceId1];
        if (workspaceParams) {
          // Create anchors for the expanded box
          const expandedAnchors = {
            top: { x: 0, y: -expandedParams.height / 2 },
            bottom: { x: 0, y: expandedParams.height / 2 },
            left: { x: -expandedParams.width / 2, y: 0 },
            right: { x: expandedParams.width / 2, y: 0 },
          };
          sourceAnchors = getAbsoluteAnchors(
            expandedAnchors,
            workspaceParams.x,
            workspaceParams.y,
          );
        }
      }
    } else {
      // Get anchors from the regular element
      const sourceElement = store.state.allSimpleElements.get(sourceElementId);
      if (!sourceElement) continue;
      const workspaceParams = sourceElement.workspaceParameters[workspaceId1];
      if (workspaceParams && workspaceParams.anchors) {
        sourceAnchors = getAbsoluteAnchors(
          workspaceParams.anchors,
          workspaceParams.x,
          workspaceParams.y,
        );
      }
    }

    // Handle destination element anchors
    if (isDestExpanded) {
      // Get anchors from the expanded workspace parameters
      const expandedParams =
        getExpandedWorkspaceParameters(destinationElementId);
      const destElement =
        store.state.allSimpleElements.get(destinationElementId);
      if (expandedParams && destElement) {
        const workspaceParams = destElement.workspaceParameters[workspaceId1];
        if (workspaceParams) {
          // Create anchors for the expanded box
          const expandedAnchors = {
            top: { x: 0, y: -expandedParams.height / 2 },
            bottom: { x: 0, y: expandedParams.height / 2 },
            left: { x: -expandedParams.width / 2, y: 0 },
            right: { x: expandedParams.width / 2, y: 0 },
          };
          destAnchors = getAbsoluteAnchors(
            expandedAnchors,
            workspaceParams.x,
            workspaceParams.y,
          );
        }
      }
    } else {
      // Get anchors from the regular element
      const destinationElement =
        store.state.allSimpleElements.get(destinationElementId);
      if (!destinationElement) continue;
      const workspaceParams =
        destinationElement.workspaceParameters[workspaceId1];
      if (workspaceParams && workspaceParams.anchors) {
        destAnchors = getAbsoluteAnchors(
          workspaceParams.anchors,
          workspaceParams.x,
          workspaceParams.y,
        );
      }
    }

    if (!sourceAnchors || !destAnchors) {
      continue;
    }

    // Calculate distances between all possible anchor pairs
    const anchorPairs = [
      // Source top to destination anchors
      {
        from: sourceAnchors.top,
        to: destAnchors.top,
        distance: calculateDistance(sourceAnchors.top, destAnchors.top),
        orientation: "vertical",
      },
      {
        from: sourceAnchors.top,
        to: destAnchors.bottom,
        distance: calculateDistance(sourceAnchors.top, destAnchors.bottom),
        orientation: "vertical",
      },
      {
        from: sourceAnchors.top,
        to: destAnchors.left,
        distance: calculateDistance(sourceAnchors.top, destAnchors.left),
        orientation: "diagonal",
      },
      {
        from: sourceAnchors.top,
        to: destAnchors.right,
        distance: calculateDistance(sourceAnchors.top, destAnchors.right),
        orientation: "diagonal",
      },

      // Source bottom to destination anchors
      {
        from: sourceAnchors.bottom,
        to: destAnchors.top,
        distance: calculateDistance(sourceAnchors.bottom, destAnchors.top),
        orientation: "vertical",
      },
      {
        from: sourceAnchors.bottom,
        to: destAnchors.bottom,
        distance: calculateDistance(sourceAnchors.bottom, destAnchors.bottom),
        orientation: "vertical",
      },
      {
        from: sourceAnchors.bottom,
        to: destAnchors.left,
        distance: calculateDistance(sourceAnchors.bottom, destAnchors.left),
        orientation: "diagonal",
      },
      {
        from: sourceAnchors.bottom,
        to: destAnchors.right,
        distance: calculateDistance(sourceAnchors.bottom, destAnchors.right),
        orientation: "diagonal",
      },

      // Source left to destination anchors
      {
        from: sourceAnchors.left,
        to: destAnchors.top,
        distance: calculateDistance(sourceAnchors.left, destAnchors.top),
        orientation: "diagonal",
      },
      {
        from: sourceAnchors.left,
        to: destAnchors.bottom,
        distance: calculateDistance(sourceAnchors.left, destAnchors.bottom),
        orientation: "diagonal",
      },
      {
        from: sourceAnchors.left,
        to: destAnchors.left,
        distance: calculateDistance(sourceAnchors.left, destAnchors.left),
        orientation: "horizontal",
      },
      {
        from: sourceAnchors.left,
        to: destAnchors.right,
        distance: calculateDistance(sourceAnchors.left, destAnchors.right),
        orientation: "horizontal",
      },

      // Source right to destination anchors
      {
        from: sourceAnchors.right,
        to: destAnchors.top,
        distance: calculateDistance(sourceAnchors.right, destAnchors.top),
        orientation: "diagonal",
      },
      {
        from: sourceAnchors.right,
        to: destAnchors.bottom,
        distance: calculateDistance(sourceAnchors.right, destAnchors.bottom),
        orientation: "diagonal",
      },
      {
        from: sourceAnchors.right,
        to: destAnchors.left,
        distance: calculateDistance(sourceAnchors.right, destAnchors.left),
        orientation: "horizontal",
      },
      {
        from: sourceAnchors.right,
        to: destAnchors.right,
        distance: calculateDistance(sourceAnchors.right, destAnchors.right),
        orientation: "horizontal",
      },
    ];

    // Find the pair with the minimum distance
    let closestPair = anchorPairs[0];
    for (let i = 1; i < anchorPairs.length; i++) {
      if (anchorPairs[i].distance < closestPair.distance) {
        closestPair = anchorPairs[i];
      }
    }

    return {
      anchorFrom: closestPair.from,
      anchorTo: closestPair.to,
      orientation: closestPair.orientation,
    };
  }
};

// Helper function to calculate distance between two points
function calculateDistance(point1, point2) {
  return Math.sqrt(
    Math.pow(point1.x - point2.x, 2) + Math.pow(point1.y - point2.y, 2),
  );
}

export const isEmptyObject = (object) => {
  return (
    object === null ||
    typeof object === "undefined" ||
    (Object.keys(object).length === 0 && object.constructor === Object) ||
    object.length === 0
  );
};

export const addElementToSelection = async ({
  elementId,
  deselectOthers,
  undoable,
}) => {
  if (deselectOthers) {
    await unselectAllElements();
  }
  if (!store.getters.currentWorkspaceSelectedElementsIds.includes(elementId)) {
    const displayExpandIcon =
      store.state.diagramMode === diagramMode.creationMode;
    drawAdapter.highlightElement(elementId, displayExpandIcon);
    if (store.state.diagramMode === diagramMode.recordingMode) {
      showItemPositions(elementId);
    }
    await store.dispatch("selectElementAction", { elementId, undoable });
    await store.dispatch("updateRightDrawerHeaderTabAction", "elements");
    await store.dispatch("updateRightDrawerElementsSubTabAction", "edit");
    await store.dispatch("showRightDrawerAction", true);
  }
};

export const selectAllElements = async () => {
  for (const element of Array.from(
    store.state.allSimpleElements.values(),
  ).filter((element) => {
    return element.parentWorkspaceIds.includes(store.state.currentWorkspaceId);
  })) {
    await addElementToSelection({
      elementId: element.id,
      deselectOthers: false,
      undoable: false,
    });
  }
};

export const unselectElement = async (elementId) => {
  unlightElement(elementId);
  hideItemPositions(elementId);
  if (store.state.allSimpleElements.get(elementId).type !== "TEXT-CLASSIC") {
    hideElementNameMover(elementId);
  }
  await store.dispatch("unselectElementAction", elementId);
};

export const unselectAllElements = async () => {
  elementIdsAvailable()
    .map((elementId) => store.state.allSimpleElements.get(elementId))
    .forEach((element) => {
      unselectElement(element.id);
    });
  if (store.getters.currentWorkspaceSelectedElementsIds.length > 0) {
    await store.dispatch("clearSelectedElementsIdsAction").then();
  }
};

function unselectSegment(segmentId) {
  drawAdapter.unselectSegment(segmentId);
}

export const unselectAllSegments = async () => {
  currentWorkspaceSegments().forEach((segment) => {
    unselectSegment(segment.id);
  });
  if (store.state.selectedSegmentsIds.length > 0) {
    await store.dispatch("clearSelectedSegmentsIdsAction");
  }
};

export const updateGraphicElementFromState = (elementId) => {
  const simpleElement = store.state.allSimpleElements.get(elementId);
  const displayShadows = store.state.displayShadows;
  drawAdapter.updateGraphicElementFromState(
    simpleElement,
    displayShadows,
    store.state.currentWorkspaceId,
  );
  if (store.state.diagramMode !== diagramMode.creationMode) {
    hideAnchorsForElement(elementId);
  }
};

export const removeElementFromSelection = (elementId) => {
  unlightElement(elementId);
  hideItemPositions(elementId);
  store.dispatch("unselectElementAction", elementId).then();
};

// Helper functions for batch operations on multiple elements
export const updateColorForSelectedElements = async (newColor) => {
  for (const elementId of store.getters.currentWorkspaceSelectedElementsIds) {
    const simpleElement = store.state.allSimpleElements.get(elementId);
    simpleElement.color = newColor;
    await store.dispatch("updateSimpleElement", {
      simpleElement,
      undoable: true,
    });
    updateGraphicElementFromState(elementId);
  }
};

export const updateTextPropertiesForSelectedElements = async ({
  textColor,
  textSize,
  textFont,
  textWeight,
  textAlign,
}) => {
  for (const elementId of store.getters.currentWorkspaceSelectedElementsIds) {
    const simpleElement = store.state.allSimpleElements.get(elementId);

    if (textColor !== undefined) simpleElement.textColor = textColor;
    if (textSize !== undefined) simpleElement.textSize = textSize;
    if (textFont !== undefined) simpleElement.textFont = textFont;
    if (textWeight !== undefined) simpleElement.textWeight = textWeight;
    if (textAlign !== undefined) simpleElement.textAlign = textAlign;

    await store.dispatch("updateSimpleElement", {
      simpleElement,
      undoable: true,
    });
    updateGraphicElementFromState(elementId);
  }
};

export const moveSelectedElementsToWorkspace = async (
  targetWorkspaceId,
  getTargetWorkspaceIdCallback,
) => {
  const selectedElementIds = [
    ...store.getters.currentWorkspaceSelectedElementsIds,
  ];
  if (selectedElementIds.length === 0) return false;

  // Store the callback but don't create the workspace yet
  let actualTargetWorkspaceId = targetWorkspaceId;
  const isCreateNewWorkspace =
    !actualTargetWorkspaceId && getTargetWorkspaceIdCallback !== null;

  // First, analyze all selected elements to find connected elements
  const currentWorkspaceId = store.state.currentWorkspaceId;
  const allConnectedElementIds = new Set();
  const connectedElementsOutsideSelection = new Set();

  // Find all connected elements for the selected elements
  for (const elementId of selectedElementIds) {
    const element = store.state.allSimpleElements.get(elementId);
    if (!element) continue;

    const connectedIds =
      element.workspaceParameters[currentWorkspaceId].destinationElementIds;
    for (const connectedId of connectedIds) {
      allConnectedElementIds.add(connectedId);
      // If the connected element is not in the selection, add it to the outside set
      if (!selectedElementIds.includes(connectedId)) {
        connectedElementsOutsideSelection.add(connectedId);
      }
    }
  }

  // If there are connected elements outside the selection, ask the user what to do
  let moveConnectedElements = null;
  if (connectedElementsOutsideSelection.size > 0) {
    try {
      // Show the dialog and wait for user decision - only once for all elements
      const decision = await showMoveElementDialog(
        connectedElementsOutsideSelection.size,
      );

      if (decision === "moveAll") {
        // User chose to move all connected elements
        moveConnectedElements = true;
      } else if (decision === "moveSelected") {
        // User chose to remove connections and move only selected elements
        moveConnectedElements = false;
      } else if (decision === "cancel") {
        // User chose to cancel the operation
        return false; // Exit the function without doing anything
      }
    } catch (error) {
      console.error("Error showing move element dialog:", error);
      return false; // Exit if there was an error
    }
  }

  // Now that we have the user's decision, create the workspace if needed
  if (isCreateNewWorkspace) {
    actualTargetWorkspaceId = await getTargetWorkspaceIdCallback();
    if (!actualTargetWorkspaceId) return false; // User cancelled workspace creation
  }

  // Handle sequence actions for all selected elements
  for (const elementId of selectedElementIds) {
    await handleSequenceActionsForMovedElement(
      elementId,
      currentWorkspaceId,
      actualTargetWorkspaceId,
    );
  }

  // If user chose to move all connected elements or there are no outside connections
  if (
    moveConnectedElements === true ||
    connectedElementsOutsideSelection.size === 0
  ) {
    // First move all selected elements
    for (const elementId of selectedElementIds) {
      await moveElementWithoutConnections(elementId, actualTargetWorkspaceId);
    }

    // Then move all connected elements that weren't in the original selection
    if (moveConnectedElements === true) {
      for (const connectedId of connectedElementsOutsideSelection) {
        // Handle sequence actions for the connected element
        await handleSequenceActionsForMovedElement(
          connectedId,
          currentWorkspaceId,
          actualTargetWorkspaceId,
        );

        await moveElementToWorkspace(
          connectedId,
          actualTargetWorkspaceId,
          true,
        );
      }
    }

    // Update all segments between the moved elements to the new workspace
    const segmentsToUpdate = currentWorkspaceSegments().filter((segment) => {
      const element1 = segment.segmentElementsInfo.element1;
      const element2 = segment.segmentElementsInfo.element2;
      return (
        (selectedElementIds.includes(element1) ||
          (moveConnectedElements === true &&
            connectedElementsOutsideSelection.has(element1))) &&
        (selectedElementIds.includes(element2) ||
          (moveConnectedElements === true &&
            connectedElementsOutsideSelection.has(element2)))
      );
    });

    for (const segment of segmentsToUpdate) {
      const foundSegment = deepClone(segment);
      // remove the old workspace
      foundSegment.parentWorkspaceId.splice(
        foundSegment.parentWorkspaceId.indexOf(currentWorkspaceId),
        1,
      );
      // add the new workspace
      foundSegment.parentWorkspaceId.push(actualTargetWorkspaceId);
      await store.dispatch("updateSegmentAction", {
        segment: foundSegment,
        undoable: true,
      });
    }
  } else if (moveConnectedElements === false) {
    // User chose to remove connections to elements outside the selection, but keep connections between selected elements

    // First, identify connections between selected elements that should be preserved
    const connectionsToPreserve = new Map(); // Map of elementId -> [connectedElementIds]

    for (const elementId of selectedElementIds) {
      const element = store.state.allSimpleElements.get(elementId);
      if (!element) continue;

      const connectedIds =
        element.workspaceParameters[currentWorkspaceId].destinationElementIds;
      const preservedConnections = connectedIds.filter((id) =>
        selectedElementIds.includes(id),
      );

      if (preservedConnections.length > 0) {
        connectionsToPreserve.set(elementId, preservedConnections);
      }
    }

    // Now move each element, removing only connections to elements outside the selection
    for (const elementId of selectedElementIds) {
      const element = store.state.allSimpleElements.get(elementId);
      if (!element) continue;

      // Get all connected elements
      const connectedIds = [
        ...element.workspaceParameters[currentWorkspaceId]
          .destinationElementIds,
      ];

      // Remove connections to elements outside the selection
      for (const connectedId of connectedIds) {
        if (!selectedElementIds.includes(connectedId)) {
          // Find and delete the segment
          const segment = Array.from(
            store.state.allSimpleSegments.values(),
          ).find(
            (segment) =>
              (segment.segmentElementsInfo.element1 === elementId &&
                segment.segmentElementsInfo.element2 === connectedId) ||
              (segment.segmentElementsInfo.element1 === connectedId &&
                segment.segmentElementsInfo.element2 === elementId),
          );

          if (segment) {
            await deleteSegment(segment.id, true, true);
          }

          // Remove from element's destinationElementIds
          const index =
            element.workspaceParameters[
              currentWorkspaceId
            ].destinationElementIds.indexOf(connectedId);
          if (index !== -1) {
            element.workspaceParameters[
              currentWorkspaceId
            ].destinationElementIds.splice(index, 1);
          }

          // Remove from connected element's destinationElementIds
          const connectedElement =
            store.state.allSimpleElements.get(connectedId);
          if (connectedElement) {
            const connIndex =
              connectedElement.workspaceParameters[
                currentWorkspaceId
              ].destinationElementIds.indexOf(elementId);
            if (connIndex !== -1) {
              connectedElement.workspaceParameters[
                currentWorkspaceId
              ].destinationElementIds.splice(connIndex, 1);
              await store.dispatch("updateSimpleElement", {
                simpleElement: connectedElement,
                undoable: true,
              });
            }
          }
        }
      }

      // Update the element
      await store.dispatch("updateSimpleElement", {
        simpleElement: element,
        undoable: true,
      });

      // Move the element to the new workspace
      await moveElementWithoutConnections(elementId, actualTargetWorkspaceId);
    }

    // Recreate segments between selected elements in the new workspace
    for (const [elementId, connectedIds] of connectionsToPreserve.entries()) {
      for (const connectedId of connectedIds) {
        // Only create the segment once (when elementId < connectedId to avoid duplicates)
        if (elementId < connectedId) {
          // Create a new segment in the target workspace
          const segmentInfo = {
            element1: elementId,
            element2: connectedId,
            anchor1: null, // We'll let the system determine the anchors
            anchor2: null,
          };

          const segmentId = uuidv4();
          const segment = {
            id: segmentId,
            segmentElementsInfo: segmentInfo,
            parentWorkspaceId: [actualTargetWorkspaceId],
          };

          await store.dispatch("updateSegmentAction", {
            segment,
            undoable: true,
          });

          // Update the elements' workspaceParameters for the new workspace
          const element1 = store.state.allSimpleElements.get(elementId);
          const element2 = store.state.allSimpleElements.get(connectedId);

          if (element1 && element2) {
            // Ensure workspaceParameters exists for the new workspace
            if (!element1.workspaceParameters[actualTargetWorkspaceId]) {
              element1.workspaceParameters[actualTargetWorkspaceId] = {
                destinationElementIds: [],
                position:
                  element1.workspaceParameters[currentWorkspaceId].position,
              };
            }

            if (!element2.workspaceParameters[actualTargetWorkspaceId]) {
              element2.workspaceParameters[actualTargetWorkspaceId] = {
                destinationElementIds: [],
                position:
                  element2.workspaceParameters[currentWorkspaceId].position,
              };
            }

            // Add the connection in the new workspace
            if (
              !element1.workspaceParameters[
                actualTargetWorkspaceId
              ].destinationElementIds.includes(connectedId)
            ) {
              element1.workspaceParameters[
                actualTargetWorkspaceId
              ].destinationElementIds.push(connectedId);
              await store.dispatch("updateSimpleElement", {
                simpleElement: element1,
                undoable: true,
              });
            }

            if (
              !element2.workspaceParameters[
                actualTargetWorkspaceId
              ].destinationElementIds.includes(elementId)
            ) {
              element2.workspaceParameters[
                actualTargetWorkspaceId
              ].destinationElementIds.push(elementId);
              await store.dispatch("updateSimpleElement", {
                simpleElement: element2,
                undoable: true,
              });
            }
          }
        }
      }
    }
  }

  return true;
};

export const deleteSelectedElements = async ({ force, undoable }) => {
  const selectedElementIds = [
    ...store.getters.currentWorkspaceSelectedElementsIds,
  ];
  for (const elementId of selectedElementIds) {
    await deleteElement({ elementId, force, undoable });
  }
};

export const addSegmentToSelection = (segmentId, deselectOthers, undoable) => {
  if (deselectOthers) {
    unselectAllSegments();
  }
  if (!store.state.selectedSegmentsIds.includes(segmentId)) {
    drawAdapter.highlightSegment(segmentId);
    store
      .dispatch("selectSegmentAction", { segmentId: segmentId, undoable })
      .then();
  }
};

export const deleteElement = async ({ elementId, force, undoable }) => {
  if (!(await deleteElementFromSequences({ elementId, force, undoable }))) {
    return;
  }
  const element = store.state.allSimpleElements.get(elementId);

  if (!element) {
    return;
  }

  await unselectElement(elementId);

  drawAdapter.deleteElement(elementId, true);

  const allSegments = store.state.allSimpleSegments;

  for (const segment1 of Array.from(allSegments.values()).filter(
    (segment) =>
      (element.workspaceParameters[
        store.state.currentWorkspaceId
      ]?.destinationElementIds.includes(segment.segmentElementsInfo.element1) &&
        element.id === segment.segmentElementsInfo.element2) ||
      (element.workspaceParameters[
        store.state.currentWorkspaceId
      ]?.destinationElementIds.includes(segment.segmentElementsInfo.element2) &&
        element.id === segment.segmentElementsInfo.element1),
  )) {
    await deleteSegment(segment1.id, true, undoable);
    await store
      .dispatch("deleteSegmentAction", { segmentId: segment1.id, undoable })
      .then();
  }

  if (element.parentWorkspaceIds.length > 1) {
    const index = element.parentWorkspaceIds.findIndex(
      (id) => id === store.state.currentWorkspaceId,
    );
    if (index !== -1) {
      element.parentWorkspaceIds.splice(index, 1);
    }
    await store.dispatch("updateSimpleElement", {
      simpleElement: element,
      undoable,
    });
    return;
  }

  drawAdapter.deleteElement(elementId, true);

  // If this element has an expanded workspace, collapse it first
  if (element.childWorkspaceId && isWorkspaceExpanded(elementId)) {
    await collapseWorkspace(elementId, undoable);
  }

  // Delete child workspace if it exists
  if (element.childWorkspaceId) {
    await deleteWorkspace(element.childWorkspaceId, force);
  }

  await store.dispatch("deleteElementAction", { elementId, undoable }).then();
};

const deleteElementFromSequences = async ({ elementId, force, undoable }) => {
  const sequences = store.state.sequences;
  let sequencesAffected = false;

  // Check if any sequence is affected
  for (const sequence of sequences.values()) {
    for (const frame of sequence.frames) {
      for (const action of frame) {
        if (
          action.data?.elementSource === elementId ||
          action.data?.elementDestination === elementId ||
          action.data?.elementId === elementId
        ) {
          sequencesAffected = true;
          break;
        }
      }
      if (sequencesAffected) break;
    }
    if (sequencesAffected) break;
  }

  if (sequencesAffected) {
    let confirmDelete = true;
    if (!store.state.loading && !force) {
      confirmDelete = window.confirm(
        "The item you are trying to delete is part of a sequence.\nAll changes will be lost. Are you sure you want to delete this item?",
      );
    }

    if (confirmDelete) {
      // Remove the element from sequences
      for (const sequence of sequences.values()) {
        for (
          let frameIndex = sequence.frames.length - 1;
          frameIndex >= 0;
          frameIndex--
        ) {
          const frame = sequence.frames[frameIndex];
          if (!frame) {
            continue;
          }
          for (
            let actionIndex = frame.length - 1;
            actionIndex >= 0;
            actionIndex--
          ) {
            const action = frame[actionIndex];
            if (
              action.data?.elementSource === elementId ||
              action.data?.elementDestination === elementId ||
              action.data.elementId === elementId
            ) {
              await deleteSequenceFrameAction({
                sequence,
                frameIndex,
                actionIndex,
                undoable,
              });
            }
          }
        }
        // Recreate first frame by default
        if (!sequence.frames[0]) {
          sequence.frames.push([]);
          await store.dispatch("updateSequenceAction", {
            id: sequence.id,
            sequence,
            undoable: false,
          });
        }
      }
      return true;
    } else {
      return false;
    }
  } else {
    return true;
  }
};

export const deleteSegment = async (segmentId, force, undoable) => {
  const simpleSegment = store.state.allSimpleSegments.get(segmentId);

  if (!simpleSegment) return;

  const { element1, element2 } = simpleSegment.segmentElementsInfo;

  // Check if the segment is part of any sequence
  const sequencesAffected = Array.from(store.state.sequences.values()).some(
    (sequence) =>
      sequence.frames.some((frame) =>
        frame.some(
          (action) =>
            (action.data?.elementSource === element1 &&
              action.data?.elementDestination === element2) ||
            (action.data?.elementSource === element2 &&
              action.data?.elementDestination === element1),
        ),
      ),
  );

  let confirmDelete = true;
  if (sequencesAffected && !store.state.loading && !force) {
    confirmDelete = window.confirm(
      "The segment you are trying to delete is part of a sequence.\nAll changes related to this segment will be lost. Are you sure you want to delete this segment?",
    );
  }

  if (confirmDelete || force) {
    for (const sequence of store.state.sequences.values()) {
      for (
        let frameIndex = 0;
        frameIndex < sequence.frames.length;
        frameIndex++
      ) {
        const frame = sequence.frames[frameIndex];
        for (
          let actionIndex = frame.length - 1;
          actionIndex >= 0;
          actionIndex--
        ) {
          const action = frame[actionIndex];
          if (
            (action.data?.elementSource === element1 &&
              action.data?.elementDestination === element2) ||
            (action.data?.elementSource === element2 &&
              action.data?.elementDestination === element1)
          ) {
            await deleteSequenceFrameAction({
              sequence,
              frameIndex,
              actionIndex,
              undoable,
            });
          }
        }
      }
    }

    // Update related elements and remove the segment
    const element1Obj = store.state.allSimpleElements.get(element1);
    const element2Obj = store.state.allSimpleElements.get(element2);

    if (element1Obj && element2Obj) {
      element1Obj.workspaceParameters[
        store.state.currentWorkspaceId
      ].destinationElementIds.delete(element2);
      element2Obj.workspaceParameters[
        store.state.currentWorkspaceId
      ].destinationElementIds.delete(element1);

      drawAdapter.deleteSegment(segmentId);
      await store.dispatch("updateSimpleElement", {
        simpleElement: element1Obj,
        undoable,
      });
      await store.dispatch("updateSimpleElement", {
        simpleElement: element2Obj,
        undoable,
      });
    }

    await store.dispatch("deleteSegmentAction", { segmentId, undoable });
  }
};

export const deleteAllGraphicObjects = () => {
  forceStopAnimation();
  Array.from(store.state.allSimpleElements.keys()).forEach((elementId) => {
    drawAdapter.deleteElement(elementId, true);
  });
  Array.from(store.state.allSimpleSegments.keys()).forEach((segmentId) => {
    drawAdapter.deleteSegment(segmentId, true);
  });
  drawAdapter.deleteAllElements(); // just to be sure
  drawAdapter.deleteAllSegments(); // just to be sure
  deleteAllExpandedWorkspaces();
};

export const highlightElement = (elementId) => {
  const displayExpandIcon =
    store.state.diagramMode === diagramMode.creationMode;
  drawAdapter.highlightElement(elementId, displayExpandIcon);
};

export const unlightElement = (elementId) => {
  drawAdapter.unlightElement(elementId);
};

export const hideElementNameMover = (elementId) => {
  drawAdapter.hideElementNameMover(elementId);
};

export const resetCursors = () => {
  drawAdapter.resetCursors();
};

export const blinkValidation = (elementId) => {
  drawAdapter.blinkValidation(elementId);
};

export const importElements = async (simpleElements) => {
  const displayShadows = store.state.displayShadows;
  await store.dispatch(
    "setAllSimpleElementsAction",
    new Map(
      simpleElements.map((element) => {
        // Set parentWorkspaceIds if it doesn't exist
        if (!element.parentWorkspaceIds) {
          element.parentWorkspaceIds = [store.state.currentWorkspaceId];
        }
        if (!element.childWorkspaceId) {
          element.childWorkspaceId = null;
        }

        // Initialize workspaceParameters if it doesn't exist
        if (!element.workspaceParameters) {
          element.workspaceParameters = {};
          // Add current position to the map for each parent workspace
          element.parentWorkspaceIds.forEach((workspaceId) => {
            element.workspaceParameters[workspaceId] = {
              x: element.x, // For backward compatibility
              y: element.y, // For backward compatibility
              width: element.width,
              height: element.height,
              textX: element.textX,
              textY: element.textY,
              anchors: element.anchors,
              destinationElementIds: element.destinationElementIds,
            };
          });
        }
        return [element.id, element];
      }),
    ),
  );
  for (const simpleElement of simpleElements.filter((element) =>
    element.parentWorkspaceIds.includes(store.state.currentWorkspaceId),
  )) {
    const workspaceParams =
      simpleElement.workspaceParameters[store.state.currentWorkspaceId];

    // Calculate absolute positions for drawing
    const absoluteAnchors = getAbsoluteAnchors(
      workspaceParams.anchors,
      workspaceParams.x,
      workspaceParams.y,
    );
    const absoluteTextX = workspaceParams.x + workspaceParams.textX;
    const absoluteTextY = workspaceParams.y + workspaceParams.textY;

    drawAdapter.createElement(
      workspaceParams.x,
      workspaceParams.y,
      simpleElement.type,
      simpleElement.name,
      absoluteTextX,
      absoluteTextY,
      simpleElement.id,
      simpleElement.color,
      simpleElement.textColor,
      simpleElement.textSize,
      simpleElement.textFont,
      simpleElement.textWeight,
      simpleElement.textAlign,
      workspaceParams.width,
      workspaceParams.height,
      absoluteAnchors,
      displayShadows,
    );

    if (
      simpleElement.workspaceParameters[store.state.currentWorkspaceId]
        ?.expandedParameters
    ) {
      await expandWorkspace({
        elementId: simpleElement.id,
        pushElements: false,
        undoable: false,
      });
    }
  }
  retraceAllSegments(); // just to remove that weird bug that makes animation reversed...
};

export const setViewbox = () => {
  const windowWidthOriginal = store.state.viewbox.windowWidth || 1920;
  const windowHeightOriginal = store.state.viewbox.windowHeight || 1080;
  let originalHeight = store.state.viewbox?.height;
  let originalWidth = store.state.viewbox?.width;
  const newWindowWidth = window.innerWidth;
  const scaleFactorX = (windowWidthOriginal || newWindowWidth) / newWindowWidth;
  const originalX = store.state.viewbox?.x;
  const ooriginalY = store.state.viewbox?.y;
  const newSvgWidth = originalWidth * scaleFactorX;
  const newSvgHeight = originalHeight * scaleFactorX;

  // First we find the original centre of the SVG
  const svgYCentre =
    ooriginalY + (windowHeightOriginal * (originalHeight / 10000)) / 2;
  // Then we find the new Y position of the top-left corner of the SVG
  const newSvgY =
    svgYCentre - (window.innerHeight * (newSvgHeight / 10000)) / 2;

  if (
    originalX === undefined ||
    ooriginalY === undefined ||
    newSvgWidth === undefined ||
    newSvgHeight === undefined
  ) {
    return;
  }

  // Set the viewbox with the top-left corner anchored.
  draw.viewbox(originalX, newSvgY, newSvgWidth, newSvgHeight);
};

export const cloneSelectedElements = async () => {
  await unselectAllElements();
  for (const elementId of store.state.copyElementIds) {
    const simpleElement = store.state.allSimpleElements.get(elementId);
    const workspaceParams =
      simpleElement.workspaceParameters[store.state.currentWorkspaceId];
    let newElementId = uuidv4();

    // Calculate absolute text position for the new element
    const absoluteTextX = workspaceParams.x + workspaceParams.textX + 20;
    const absoluteTextY = workspaceParams.y + workspaceParams.textY + 20;

    await createNewElementAtPosition(
      workspaceParams.x + 20,
      workspaceParams.y + 20,
      simpleElement.type,
      simpleElement.name,
      absoluteTextX,
      absoluteTextY,
      newElementId,
      simpleElement.color,
      simpleElement.textColor,
      simpleElement.textSize,
      simpleElement.textFont,
      simpleElement.textWeight,
      simpleElement.textAlign,
      workspaceParams.width,
      workspaceParams.height,
    );
    await addElementToSelection({
      elementId: newElementId,
      deselectOthers: false,
      undoable: false,
    });
  }
};

export const showVisibilityToggles = () => {
  drawAdapter.showVisibilityToggles();
};

export const hideVisibilityToggles = () => {
  drawAdapter.hideVisibilityToggles();
};

export const showItemPositions = (elementId) => {
  const sequenceId = store.state.currentSequenceId;
  const sequence = store.state.sequences.get(sequenceId);
  if (!sequence) return;

  const positions = [];

  sequence.frames.forEach((frame) => {
    const action = frame.find(
      (action) =>
        action.metadata.type === ELEMENT_POSITION &&
        action.data.elementId === elementId,
    );
    if (action) {
      positions.push({
        x: action.data.position.x,
        y: action.data.position.y,
        width: action.data.position.width,
        height: action.data.position.height,
        interpolated: action.metadata.interpolated,
      });
    }
  });
  drawAdapter.showItemPositions(elementId, positions);
};

export const hideItemPositions = (elementId) => {
  drawAdapter.hideItemPositions(elementId);
};

export const importSegments = async (simpleSegments) => {
  await store.dispatch(
    "setAllSimpleSegmentsAction",
    new Map(
      simpleSegments.map((segment) => {
        if (!segment.parentWorkspaceId) {
          segment.parentWorkspaceId = [store.state.rootWorkspaceId];
        }
        return [segment.id, segment];
      }),
    ),
  );
};

export const toggleRecordingMode = async (switchTab) => {
  if (store.state.diagramMode === diagramMode.recordingMode && !switchTab) {
    return;
  }

  // Collapse all expanded workspaces before entering animation mode
  await collapseAllExpandedWorkspaces();

  // Ensure we have a sequence for the current workspace
  let currentSequence = null;
  const currentWorkspaceId = store.state.currentWorkspaceId;

  // First, try to find an existing sequence for the current workspace
  const workspaceSequences = currentWorkspaceSequences();
  if (workspaceSequences.length > 0) {
    // Use the first sequence found for this workspace
    currentSequence = workspaceSequences[0];
    await store.dispatch("updateCurrentSequenceIdAction", {
      sequenceId: currentSequence.id,
    });
  } else {
    // No sequence exists for this workspace, create one
    currentSequence = await createSequence({
      parentWorkspaceId: currentWorkspaceId,
    });
  }

  redrawElementsAtFrame(currentSequence.id, currentSequence.currentFrame);
  await stopSequence(currentSequence.id);
  hideAnchorsForNonConnectedItems();
  setAppropriateSelectionMode();
  await store.dispatch("updateDiagramModeAction", diagramMode.recordingMode);
  if (!store.state.sequences || store.state.sequences.size === 0) {
    await createSequence({});
  }
  if (currentSequence.id) {
    setupObjectsAtFrame(
      currentSequence.id,
      store.state.sequences.get(currentSequence.id).currentFrame,
    );
  } else {
    setRecordingBackground();
  }
  store.getters.currentWorkspaceSelectedElementsIds.forEach((elementId) =>
    showItemPositions(elementId),
  );
  if (switchTab) {
    store.dispatch("updateHeaderTabAction", 1).then();
  }
  repairSequence(currentSequence.id);
  removeExpandIconsAndMenus();
};

export const togglePlaybackMode = async (switchTab) => {
  if (store.state.diagramMode === diagramMode.playbackMode && !switchTab) {
    return;
  }

  // Collapse all expanded workspaces before entering presentation mode
  await collapseAllExpandedWorkspaces();

  // Ensure we have a sequence for the current workspace
  let currentSequence = null;
  const currentWorkspaceId = store.state.currentWorkspaceId;

  // First, try to find an existing sequence for the current workspace
  const workspaceSequences = currentWorkspaceSequences();
  if (workspaceSequences.length > 0) {
    // Use the first sequence found for this workspace
    currentSequence = workspaceSequences[0];
    await store.dispatch("updateCurrentSequenceIdAction", {
      sequenceId: currentSequence.id,
    });
  } else {
    // No sequence exists for this workspace, create one
    currentSequence = await createSequence({
      parentWorkspaceId: currentWorkspaceId,
    });
  }

  await store.dispatch("updateSelectionModeAction", selectionMode.grabber);
  setAppropriateSelectionMode();
  await stopSequence(currentSequence.id);
  hideAnchorsForNonConnectedItems();
  await store
    .dispatch("updateDiagramModeAction", diagramMode.playbackMode)
    .then();
  setupObjectsAtFrame(currentSequence.id, currentSequence.currentFrame);
  immersiveViewOpacity(0);
  store.dispatch("showRightDrawerAction", false).then();
  hideVisibilityToggles();
  hideElementPaths();
  await unselectAllElements();
  await unselectAllSegments();
  resetCursors();
  if (switchTab) {
    store.dispatch("updateHeaderTabAction", 2).then();
  }
  repairSequence(store.state.currentSequenceId);
  removeExpandIconsAndMenus();
};

export const toggleCreationMode = async (switchTab) => {
  if (store.state.diagramMode === diagramMode.creationMode && !switchTab) {
    return;
  }
  await stopSequence(store.state.currentSequenceId);
  setupElementsForCreation();
  elementIdsAvailable()
    .map((elementId) => store.state.allSimpleElements.get(elementId))
    .forEach((simpleElement) => {
      const element = drawAdapter.parameters.allElements.get(simpleElement.id);
      if (
        simpleElement.workspaceParameters[store.state.currentWorkspaceId]
          ?.destinationElementIds.length === 0
      ) {
        configureElementAnchors(element.id);
      }
    });
  drawAdapter.parameters.svgContainer.panZoom({
    zoomMin: 0.2,
    zoomMax: 20,
    panning: true,
  });
  setAppropriateSelectionMode();
  store.dispatch("updateDiagramModeAction", diagramMode.creationMode).then();
  resetEntities();
  hideVisibilityToggles();
  hideImmersiveViewbox();
  if (switchTab) {
    store.dispatch("updateHeaderTabAction", 0).then();
  }
};

export const toggleRectangularSelectMode = () => {
  drawAdapter.setPanning(false);
  store.dispatch("updateSelectionModeAction", selectionMode.pointer).then();
};

export const toggleSelectMode = () => {
  drawAdapter.setPanning(true);
  store.dispatch("updateSelectionModeAction", selectionMode.grabber).then();
};

export const setDefaultShape = (shape) => {
  store.dispatch("setDefaultShapeAction", shape).then();
};

export const resetEntities = () => {
  drawAdapter.resetEntities();
};

export const hideElementPaths = () => {
  drawAdapter.hideElementPaths();
};

export const hideImmersiveViewbox = () => {
  drawAdapter.hideImmersiveViewbox();
};

export const hideAnchorsForNonConnectedItems = () => {
  elementIdsAvailable().forEach((elementId) => {
    const element = store.state.allSimpleElements.get(elementId);
    const workspaceId = getEffectiveWorkspaceIds(element.id)[0];
    if (
      element.workspaceParameters[workspaceId].destinationElementIds.length ===
      0
    ) {
      hideAnchorsForElement(element.id);
    }
  });
};

export const hideAnchorsForElement = (elementId) => {
  drawAdapter.hideConnectedAnchors(elementId);
};

export const startDragging = (elementId, offsetX, offsetY) => {
  const dragParams = store.state.dragParams;
  dragParams.dragFromElementStarted = true;
  dragParams.dragSourceElement = elementId;
  store.dispatch("updateDragParamsAction", dragParams);

  drawAdapter.startDragging(elementId, offsetX, offsetY);
};

export const endDragging = async (elementId) => {
  const dragParams = store.state.dragParams;

  drawAdapter.endDragging(store.state.dragParams.dragSourceElement);
  const sequence = deepClone(
    store.state.sequences.get(store.state.currentSequenceId),
  );
  if (dragParams.dragDestinationElement) {
    await addSendDataActionToSequence(
      elementId,
      dragParams.dragDestinationElement,
      sequence,
    );
    let originalSpeed = store.state.millisecondsPerProcessingUnit;
    store.dispatch("updateSpeedAction", { millisecondsPerProcessingUnit: 300 });
    await playFrame(
      store.state.currentSequenceId,
      store.state.sequences.get(store.state.currentSequenceId).currentFrame,
    );
    setupObjectsAtFrame(
      store.state.currentSequenceId,
      store.state.sequences.get(store.state.currentSequenceId).currentFrame,
      true,
    );
    store.dispatch("updateSpeedAction", {
      millisecondsPerProcessingUnit: originalSpeed,
    });
  }
  dragParams.dragFromElementStarted = false;
  dragParams.dragSourceElement = null;
  dragParams.dragDestinationElement = null;
  store.dispatch("updateDragParamsAction", dragParams);
};

export const getPositionFromStoredElement = (element) => {
  const currentWorkspaceId = store.state.currentWorkspaceId;
  return {
    x: element.workspaceParameters[currentWorkspaceId].x,
    y: element.workspaceParameters[currentWorkspaceId].y,
  };
};

export const getPositionFromCurrentView = () => {
  return {
    x: draw.viewbox().x,
    y: draw.viewbox().y,
  };
};

export const getPositionFromGraphicElement = (element) => {
  return {
    x: element.graphicElement.entity.cx(),
    y: element.graphicElement.entity.cy(),
  };
};

/**
 * Moves an element to a specific position and updates the state
 * @param {string} elementId - The ID of the element to move
 * @param {number} x - The new x coordinate
 * @param {number} y - The new y coordinate
 * @param {boolean} undoable - Whether this action should be undoable
 * @returns {Promise<void>}
 */
export const moveElementToPosition = async (
  elementId,
  x,
  y,
  undoable = true,
) => {
  const element = store.state.allSimpleElements.get(elementId);
  if (!element) {
    console.error(`Element with ID ${elementId} not found`);
    return;
  }

  // Use draw-adapter to set the position
  const updatedElement = drawAdapter.setPosition(elementId, x, y);

  // Update the element in the store
  await store.dispatch("updateElement", { element: updatedElement, undoable });

  // Retrace all segments connected to this element
  retraceAllSegments(elementId);
};

export function setAppropriateSelectionMode() {
  if (store.state.selectionMode === selectionMode.grabber) {
    drawAdapter.setPanning(true);
  } else if (store.state.selectionMode === selectionMode.pointer) {
    drawAdapter.setPanning(false);
  }
}

export const getDeserializedWorkspaceWithMetadata = () => {
  const workspace = getDeserialisedWorkspace();
  workspace.header = "smlx";
  workspace.version = workspace.version || "0.2.0";
  return workspace;
};

export const getDeserialisedWorkspace = () => {
  return {
    allSimpleElements: Array.from(store.state.allSimpleElements.values()).map(
      (element) => {
        return {
          id: element.id,
          name: element.name,
          type: element.type,
          color: element.color,
          textColor: element.textColor,
          textFont: element.textFont,
          textSize: element.textSize,
          textWeight: element.textWeight,
          diagramId: element.diagramId,
          parentWorkspaceIds: element.parentWorkspaceIds,
          childWorkspaceId: element.childWorkspaceId,
          workspaceParameters: element.workspaceParameters,
        };
      },
    ),
    allSimpleSegments: Array.from(store.state.allSimpleSegments.values()),
    diagramName: store.state.diagramName,
    diagramMode: store.state.diagramMode,
    sequences: Array.from(store.state.sequences.values()),
    currentSequenceId: store.state.currentSequenceId,
    selectedElementsIds: store.getters.currentWorkspaceSelectedElementsIds,
    selectedSegmentsIds: store.state.selectedSegmentsIds,
    millisecondsPerProcessingUnit: store.state.millisecondsPerProcessingUnit,
    viewbox: store.state.viewbox,
    snapToGrid: store.state.snapToGrid,
    displayGrid: store.state.displayGrid,
    autoIncrementFrame: store.state.autoIncrementFrame,
    diagramId: store.state.diagramId,
    rootWorkspaceId: store.state.rootWorkspaceId,
    currentWorkspaceId: store.state.currentWorkspaceId,
    workspaceIds: store.state.workspaceIds,
  };
};

export const holdWorkspace = () => {
  return {
    diagramName: store.state.diagramName,
    diagramMode: store.state.diagramMode,
    selectionMode: store.state.selectionMode,
    allSimpleElements: deepClone(
      Array.from(store.state.allSimpleElements.values()),
    ),
    allSimpleSegments: deepClone(
      Array.from(store.state.allSimpleSegments.values()),
    ),
    sequences: deepClone(Array.from(store.state.sequences.values())),
    currentSequenceId: store.state.currentSequenceId,
    selectedElementsIds: [...store.getters.currentWorkspaceSelectedElementsIds],
    selectedSegmentsIds: [...store.state.selectedSegmentsIds],
    millisecondsPerProcessingUnit: store.state.millisecondsPerProcessingUnit,
    dirty: store.state.dirty,
    viewbox: deepClone(store.state.viewbox),
    defaultShape: store.state.defaultShape,
    snapshots: store.state.snapshots,
    displayGrid: store.state.displayGrid,
    snapToGrid: store.state.snapToGrid,
    autoIncrementFrame: store.state.autoIncrementFrame,
    diagramId: store.state.diagramId,
    rootWorkspaceId: store.state.rootWorkspaceId,
    currentWorkspaceId: store.state.currentWorkspaceId,
    workspaceIds: store.state.workspaceIds,
  };
};

export const fetchWorkspace = async (workspace) => {
  if (isEmptyObject(workspace)) {
    return;
  }
  store.dispatch("updateLoadingAction", true).then();
  deleteAllGraphicObjects();

  // save tutorial state before
  const tutorialBefore = deepClone(store.state.tutorial);
  store.dispatch("resetState", false).then();

  // restore value of tutorial state
  store.dispatch("updateTutorialAction", tutorialBefore).then();

  // put back diagram name and snapshots
  await store.dispatch("updateDiagramNameAction", workspace.diagramName).then();
  await store.dispatch("updateSnapshotsAction", workspace.snapshots).then();

  await importSegments(workspace.allSimpleSegments);

  await importElements(workspace.allSimpleElements);
  await store.dispatch(
    "setSequencesAction",
    new Map(workspace.sequences.map((sequence) => [sequence.id, sequence])),
  );
  await selectSequence(workspace.currentSequenceId);
  await store
    .dispatch("updateSpeedAction", {
      millisecondsPerProcessingUnit: workspace.millisecondsPerProcessingUnit,
      undoable: false,
    })
    .then();
  await store.dispatch("updateDirtyAction", workspace.dirty).then();
  await store.dispatch("setViewboxAction", workspace.viewbox).then();
  await store
    .dispatch("updateSelectionModeAction", workspace.selectionMode)
    .then();
  await store.dispatch("setDefaultShapeAction", workspace.defaultShape).then();
  setViewbox();
  await store.dispatch("updateLoadingAction", false).then();
  await store.dispatch("updateDisplayGridAction", workspace.displayGrid).then();
  await store.dispatch("updateSnapToGridAction", workspace.snapToGrid).then();
  await store
    .dispatch("updateAutoIncrementFrameAction", workspace.autoIncrementFrame)
    .then();
  await store.dispatch("setDiagramIdAction", workspace.diagramId).then();
  await store.dispatch("setWorkspaceIdsAction", workspace.workspaceIds);
  await store.dispatch("setRootWorkspaceIdAction", workspace.rootWorkspaceId);
  await enterWorkspace(workspace.currentWorkspaceId);
  changeBackground();
};

export function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (obj instanceof Map) {
    const clonedMap = new Map();
    for (const [key, value] of obj) {
      clonedMap.set(key, deepClone(value));
    }
    return clonedMap;
  } else if (obj instanceof Set) {
    const clonedSet = new Set();
    for (const value of obj) {
      clonedSet.add(deepClone(value));
    }
    return clonedSet;
  } else if (obj instanceof Array) {
    return obj.map((x) => deepClone(x));
  } else {
    const clonedObj = {};
    for (const key in obj) {
      clonedObj[key] = deepClone(obj[key]);
    }
    return clonedObj;
  }
}

function base64EncodeUnicode(str) {
  return btoa(
    encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) =>
      String.fromCharCode(parseInt(p1, 16)),
    ),
  );
}

async function inlineSvgImages(svgElement) {
  const images = svgElement.querySelectorAll("image");
  const promises = Array.from(images).map(async (image) => {
    const usedAttr = image.hasAttribute("xlink:href")
      ? "xlink:href"
      : image.hasAttribute("href")
        ? "href"
        : null;
    if (!usedAttr) {
      return;
    }

    const href = image.getAttribute(usedAttr);
    // Skip if already a data URL.
    if (href && !href.startsWith("data:")) {
      try {
        const response = await fetch(href, { mode: "cors" });
        if (!response.ok) {
          console.warn(`Failed to fetch image: ${href}`);
          return;
        }
        const blob = await response.blob();
        const reader = new FileReader();
        await new Promise((resolve) => {
          reader.onload = () => {
            // Set the data URL on the same attribute originally used.
            image.setAttribute(usedAttr, reader.result);
            resolve();
          };
          reader.readAsDataURL(blob);
        });
      } catch (error) {
        console.error(`Failed to inline image: ${href}`, error);
      }
    }
  });

  await Promise.all(promises);
}

export const recordToVideo = async () => {
  // Get the starting time.
  const startTime = new Date();

  // Create and style the stopwatch element.
  const stopwatch = document.createElement("div");
  stopwatch.style.position = "fixed";
  stopwatch.style.top = "80px";
  stopwatch.style.right = "80px";
  stopwatch.style.zIndex = "2000";
  stopwatch.style.color = "red";
  stopwatch.style.fontWeight = "bolder";
  stopwatch.style.fontSize = "xx-large";
  document.body.appendChild(stopwatch);

  // Add a recording indicator
  const recordingIndicator = document.createElement("div");
  recordingIndicator.style.position = "fixed";
  recordingIndicator.style.top = "130px";
  recordingIndicator.style.right = "80px";
  recordingIndicator.style.zIndex = "2000";
  recordingIndicator.style.color = "red";
  recordingIndicator.style.fontWeight = "bold";
  recordingIndicator.style.fontSize = "large";
  recordingIndicator.textContent = "● RECORDING";
  recordingIndicator.style.display = "flex";
  recordingIndicator.style.alignItems = "center";
  recordingIndicator.style.gap = "5px";
  document.body.appendChild(recordingIndicator);

  function updateStopwatch() {
    const elapsedTime = new Date() - startTime;
    const minutes = Math.floor((elapsedTime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((elapsedTime % (1000 * 60)) / 1000);
    const milliseconds = Math.floor(elapsedTime % 1000);
    stopwatch.textContent = `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}.${milliseconds.toString().padStart(3, "0")}`;
  }
  updateStopwatch();

  // Get the SVG element.
  const svg = document.querySelector("svg");

  // Calculate the optimal canvas size - use a scale factor for better quality
  // The higher the factor, the better the quality but more performance impact
  const scaleFactor = 1.5;
  const canvasWidth = window.innerWidth * scaleFactor;
  const canvasHeight = window.innerHeight * scaleFactor;

  // Use or create a canvas element.
  const canvas =
    document.querySelector("canvas") || document.createElement("canvas");
  if (!canvas.parentNode) {
    canvas.style.display = "none"; // Hide the canvas
    document.body.appendChild(canvas);
  }

  // Set the canvas dimensions for higher quality
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;
  const ctx = canvas.getContext("2d");
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = "high";

  // Inline any external images in the SVG once before starting recording
  await inlineSvgImages(svg);

  // Pre-create the image element once
  const img = new Image();

  // Prepare to record the canvas with high framerate
  const chunks = [];
  const stream = canvas.captureStream(60); // Reduced from 200 to 60fps for stability

  // Enhanced recording options for better quality
  const options = {
    videoBitsPerSecond: 15000000, // Increased bitrate to 15Mbps
    mimeType: "video/webm;codecs=vp9", // VP9 is generally higher quality
  };

  try {
    mediaRecorder = new MediaRecorder(stream, options);
  } catch (e) {
    console.error("MediaRecorder initialization failed:", e);
    // Fallback to lower settings if the initial ones fail
    try {
      mediaRecorder = new MediaRecorder(stream, {
        videoBitsPerSecond: 8000000,
        mimeType: "video/webm",
      });
    } catch (e2) {
      console.error("Fallback MediaRecorder initialization also failed:", e2);
      stopwatch.remove();
      recordingIndicator.remove();
      return;
    }
  }

  mediaRecorder.ondataavailable = (event) => {
    if (event.data && event.data.size > 0) {
      chunks.push(event.data);
    }
  };

  mediaRecorder.onstop = () => {
    // Create a container for the video and overlay.
    const videoContainer = document.createElement("div");
    videoContainer.style.zIndex = "2000";
    videoContainer.style.position = "fixed";
    videoContainer.style.top = "0";
    videoContainer.style.left = "0";
    videoContainer.style.width = "100%";
    videoContainer.style.height = "100%";
    videoContainer.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
    // Center the video element.
    videoContainer.style.display = "flex";
    videoContainer.style.flexDirection = "column";
    videoContainer.style.justifyContent = "center";
    document.body.appendChild(videoContainer);

    // Create the video element.
    const video = document.createElement("video");
    video.controls = true; // <-- native controls visible
    video.style.display = "block";
    video.style.margin = "0 auto";
    video.style.maxWidth = "90%";
    video.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
    videoContainer.appendChild(video);

    // Create a Blob and object URL for the video.
    const videoBlob = new Blob(chunks, { type: "video/webm" });
    const videoURL = URL.createObjectURL(videoBlob);
    video.src = videoURL;
    video.play();

    // Create a container for the buttons (stacked vertically).
    const buttonContainer = document.createElement("div");
    buttonContainer.style.position = "fixed";
    buttonContainer.style.top = "50px";
    buttonContainer.style.right = "10px";
    buttonContainer.style.zIndex = "2001";
    buttonContainer.style.display = "flex";
    buttonContainer.style.flexDirection = "column";
    buttonContainer.style.gap = "10px";
    videoContainer.appendChild(buttonContainer);

    // Create a Download button.
    const downloadButton = document.createElement("button");
    downloadButton.textContent = "Download Video";
    downloadButton.style.backgroundColor = "#4b7096";
    downloadButton.style.border = "none";
    downloadButton.style.borderRadius = "5px";
    downloadButton.style.padding = "10px 20px";
    downloadButton.style.fontSize = "16px";
    downloadButton.style.cursor = "pointer";
    downloadButton.style.color = "#fff";
    downloadButton.style.transition = "background-color 0.2s ease";
    downloadButton.addEventListener("mouseenter", () => {
      downloadButton.style.backgroundColor = "#8ebef3";
    });
    downloadButton.addEventListener("mouseleave", () => {
      downloadButton.style.backgroundColor = "#4b7096";
    });
    downloadButton.addEventListener("click", () => {
      // Note: MediaRecorder produces WebM, so we download as WebM.
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = videoURL;
      a.download = "recording.webm";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    });
    buttonContainer.appendChild(downloadButton);

    // Create a Close button.
    const closeButton = document.createElement("button");
    closeButton.textContent = "Close";
    closeButton.style.backgroundColor = "#494949";
    closeButton.style.border = "none";
    closeButton.style.borderRadius = "5px";
    closeButton.style.padding = "10px 20px";
    closeButton.style.fontSize = "16px";
    closeButton.style.cursor = "pointer";
    closeButton.style.color = "#fff";
    closeButton.style.transition = "background-color 0.2s ease";
    closeButton.addEventListener("mouseenter", () => {
      closeButton.style.backgroundColor = "#806e71";
    });
    closeButton.addEventListener("mouseleave", () => {
      closeButton.style.backgroundColor = "#494949";
    });
    closeButton.addEventListener("click", () => {
      videoContainer.remove();
      stopwatch.remove();
      recordingIndicator.remove();
    });
    buttonContainer.appendChild(closeButton);

    // Remove the recording indicator
    recordingIndicator.remove();
  };

  mediaRecorder.start();

  // Automatically stop recording after 3 minutes.
  setTimeout(() => {
    if (store.state.recordingVideo === true) {
      store.dispatch("updateRecordingVideo", false);
      if (mediaRecorder.state !== "inactive") {
        mediaRecorder.stop();
      }
    }
  }, 180000);

  // Use requestAnimationFrame for smoother animation
  // This is a more optimized approach than the sleep() based loop
  let animationFrameId;
  let lastDrawTime = 0;
  const minDrawInterval = 1000 / 60; // Target 60fps

  // Performance tracking variables
  let frameCount = 0;
  let lastFpsUpdateTime = performance.now();

  // Create a serializer once instead of creating it each frame
  const serializer = new XMLSerializer();

  // Add performance indicator
  const performanceIndicator = document.createElement("div");
  performanceIndicator.style.position = "fixed";
  performanceIndicator.style.top = "160px";
  performanceIndicator.style.right = "80px";
  performanceIndicator.style.zIndex = "2000";
  performanceIndicator.style.color = "#ffcc00";
  performanceIndicator.style.fontWeight = "bold";
  performanceIndicator.style.fontSize = "medium";
  document.body.appendChild(performanceIndicator);

  // Optimized SVG capturing function
  async function captureSvgFrame() {
    // Only add the timer effect once per frame
    updateStopwatch();

    // Capture the SVG with optimized process
    const svgString = serializer.serializeToString(svg);
    const encodedSvg = base64EncodeUnicode(svgString);
    const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${encodedSvg}`;

    return new Promise((resolve) => {
      img.onload = () => {
        // Clear the canvas and draw with scaling
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.save();
        // Scale up to match the higher resolution canvas
        ctx.scale(scaleFactor, scaleFactor);
        ctx.drawImage(img, 0, 0);
        ctx.restore();

        // Track performance
        frameCount++;
        const now = performance.now();
        if (now - lastFpsUpdateTime > 1000) {
          // Update every second
          const fps = Math.round(
            (frameCount * 1000) / (now - lastFpsUpdateTime),
          );
          performanceIndicator.textContent = `Recording: ${fps} FPS`;
          frameCount = 0;
          lastFpsUpdateTime = now;

          // Adjust color based on performance
          if (fps >= 50) {
            performanceIndicator.style.color = "#00ff00"; // Green for good performance
          } else if (fps >= 30) {
            performanceIndicator.style.color = "#ffcc00"; // Yellow for acceptable
          } else {
            performanceIndicator.style.color = "#ff0000"; // Red for poor performance
          }
        }

        resolve();
      };
      img.src = dataUrl;
    });
  }

  const renderFrame = async (timestamp) => {
    if (!store.state.recordingVideo) {
      // Clean up
      cancelAnimationFrame(animationFrameId);
      performanceIndicator.remove();
      return;
    }

    // Throttle frame updates to avoid overloading
    if (timestamp - lastDrawTime >= minDrawInterval) {
      await captureSvgFrame();
      lastDrawTime = timestamp;
    }

    // Schedule next frame
    animationFrameId = requestAnimationFrame(renderFrame);
  };

  // Start the animation loop
  animationFrameId = requestAnimationFrame(renderFrame);
};

export const stopRecording = () => {
  if (mediaRecorder && mediaRecorder.state !== "inactive") {
    mediaRecorder.stop();
  }
  store.dispatch("updateRecordingVideo", false);
};

export const resetAll = async () => {
  forceStopAnimation();
  await store.dispatch("updateLoadingAction", true);
  resetFileHandle();
  setSaveDafault(null);
  deleteAllGraphicObjects();
  await store.dispatch("resetState", { undoable: false });
  initialise();
  setup();
  await store.dispatch("updateLoadingAction", false);
  changeBackground();
};

export const saveWorkspaceAs = async () => {
  let saveAborted = false;
  let workspace = getDeserializedWorkspaceWithMetadata();
  const filename = await saveFile(store.state.diagramName, workspace).catch(
    () => {
      saveAborted = true;
    },
  );
  if (saveAborted) {
    return false;
  }
  if (filename) {
    store.dispatch("updateDiagramNameAction", filename).then();
    saveWorkspace().then(); //just in case the user picked another name, we need to update and resave the file silently
  }
  store.dispatch("setLatestSnapshotToNotDirtyAction").then();
  return true;
};

export const saveWorkspace = async () => {
  let workspace = getDeserializedWorkspaceWithMetadata();
  if (saveDefault === saveTypes.browser) {
    window.localStorage.setItem(
      store.state.diagramName,
      JSON.stringify(workspace),
    );
  } else {
    let saveAborted = false;
    const filename = await updateFile(workspace).catch(() => {
      saveAborted = true;
    });
    if (saveAborted) {
      return;
    }
    if (filename) {
      store.dispatch("updateDiagramNameAction", filename).then();
    }
  }
  store.dispatch("setLatestSnapshotToNotDirtyAction").then();
};

export const loadWorkspaceFromDeserialisedContent = async (
  content,
  errorMessage,
) => {
  if (store.state.dirty && !store.state.tutorial.started) {
    const confirm = window.confirm(
      "You have un-saved changes. Do you still want to proceed?",
    );
    if (!confirm) {
      return;
    }
  }
  try {
    const fileData = JSON.parse(content);
    if (fileData.header !== "smlx") {
      alert("This is not a valid Simulaction project. Please try another one.");
      return false;
    }
    await loadWorkspace(fileData);
  } catch (ignored) {
    console.error(ignored);
    alert(errorMessage);
    return false;
  }
  store.dispatch("updateDirtyAction", false).then();
  return true;
};

export const loadWorkspace = async (workspace) => {
  await resetAll();
  if (workspace.rootWorkspaceId && workspace.workspaceIds) {
    await store.dispatch("setRootWorkspaceIdAction", workspace.rootWorkspaceId);
    await store.dispatch("setWorkspaceIdsAction", workspace.workspaceIds);
    await enterWorkspace(
      workspace.currentWorkspaceId || store.state.rootWorkspaceId,
    );
  } else {
    await store.dispatch("initializeRootWorkspaceAction");
  }

  if (workspace.allSimpleSegments) {
    await importSegments(workspace.allSimpleSegments);
  }
  await importElements(workspace.allSimpleElements);
  workspace.sequences.forEach((sequence) => {
    createSequence({
      id: sequence.id,
      name: sequence.name,
      frames: sequence.frames,
      isPlaying: sequence.isPlaying,
      currentFrame: sequence.currentFrame,
      parentWorkspaceId: sequence.parentWorkspaceId,
      undoable: false,
    });
  });
  workspace.selectedSegmentsIds?.forEach((segmentId) => {
    addSegmentToSelection(segmentId, false);
  });
  await selectSequence(workspace.currentSequenceId);
  store
    .dispatch("updateSpeedAction", {
      millisecondsPerProcessingUnit: workspace.millisecondsPerProcessingUnit,
    })
    .then();
  await store.dispatch("updateDiagramNameAction", workspace.diagramName);
  await store.dispatch("setLatestSnapshotToNotDirtyAction");
  await store.dispatch("setViewboxAction", workspace.viewbox);
  await store.dispatch(
    "updateAutoIncrementFrameAction",
    workspace.autoIncrementFrame,
  );
  await store.dispatch("updateSnapToGridAction", workspace.snapToGrid);
  await store.dispatch("updateDisplayGridAction", workspace.displayGrid);
  await store.dispatch("setDiagramIdAction", workspace.diagramId || uuidv4());
  setViewbox();
  changeBackground();
  await store.dispatch("updateResetTourAction", true);
};

const loadFromSnapshot = async (snapshot) => {
  deleteAllGraphicObjects();
  //keep diagram name and snapshots
  const diagramName = store.state.diagramName;
  const snapshots = store.state.snapshots;
  await store.dispatch("resetState", false);
  // put back diagram name and snapshots
  await store.dispatch("updateDiagramNameAction", diagramName);
  await store.dispatch("updateSnapshotsAction", snapshots);
  await store.dispatch("enterWorkspaceAction", {
    workspaceId: snapshot.currentWorkspaceId,
  });
  await importSegments(snapshot.allSimpleSegments);

  await importElements(snapshot.allSimpleElements);
  await store.dispatch(
    "setSequencesAction",
    new Map(snapshot.sequences.map((sequence) => [sequence.id, sequence])),
  );
  if (snapshot.currentSequenceId) {
    await selectSequence(snapshot.currentSequenceId);
  }
  store
    .dispatch("updateSpeedAction", {
      millisecondsPerProcessingUnit: snapshot.millisecondsPerProcessingUnit,
      undoable: false,
    })
    .then();
  await store.dispatch("updateDirtyAction", snapshot.dirty);
  await store.dispatch("setViewboxAction", snapshot.viewbox);
  await store.dispatch("setWorkspaceIdsAction", snapshot.workspaceIds);
  await store.dispatch("setRootWorkspaceIdAction", snapshot.rootWorkspaceId);
  await enterWorkspace(snapshot.currentWorkspaceId);
  switch (snapshot.diagramMode) {
    case "CREATION_MODE":
      await toggleCreationMode(true);
      break;
    case "RECORDING_MODE":
      await toggleRecordingMode(true);
      break;
    case "PLAYBACK_MODE":
      await togglePlaybackMode(true);
      break;
    default:
      console.error("Unknown diagram mode", snapshot.diagramMode);
  }
  setViewbox();
};

export const undo = async () => {
  store.dispatch("updateLoadingAction", true);
  for (let i = store.state.snapshots.length - 1; i >= 0; i--) {
    if (store.state.snapshots[i].active) {
      let previousIndex = i - 1;
      if (previousIndex >= 0) {
        store
          .dispatch("updateSnapshotActive", { index: i, active: false })
          .then();
        store
          .dispatch("updateSnapshotActive", {
            index: previousIndex,
            active: true,
          })
          .then();
        await loadFromSnapshot(store.state.snapshots[previousIndex]);
      }
      break;
    }
  }
  store.dispatch("updateLoadingAction", false);
};

export const redo = async () => {
  store.dispatch("updateLoadingAction", true);
  for (let i = 0; i < store.state.snapshots.length; i++) {
    if (store.state.snapshots[i].active) {
      let nextIndex = i + 1;
      if (nextIndex <= store.state.snapshots.length - 1) {
        store
          .dispatch("updateSnapshotActive", { index: i, active: false })
          .then();
        store
          .dispatch("updateSnapshotActive", { index: nextIndex, active: true })
          .then();
        await loadFromSnapshot(store.state.snapshots[nextIndex]);
      }
      break;
    }
  }
  store.dispatch("updateLoadingAction", false);
};

export const sleep = async (time) => {
  return new Promise((r) => setTimeout(r, time));
};

Array.prototype.delete = function (element) {
  const index = this.indexOf(element);
  if (index > -1) {
    this.splice(index, 1);
  }
};

Map.prototype.copy = function () {
  return new Map(JSON.parse(JSON.stringify(Array.from(this))));
};

Map.prototype.clone = function () {
  return deepClone(this);
};

window.addEventListener("resize", () => {
  if (!store.state.sequences?.get(store.state.currentSequenceId)?.isPlaying) {
    setViewbox();
  }
});

export const createWorkspace = async (elementId, providedWorkspaceId) => {
  const workspaceId = providedWorkspaceId || uuidv4();
  const foundElement = store.state.allSimpleElements.get(elementId);
  if (foundElement.childWorkspaceId) {
    return foundElement.childWorkspaceId;
  }

  // Create the workspace
  await store.dispatch("createWorkspaceAction", { elementId, workspaceId });

  // Set the childWorkspaceId for the element
  const element = deepClone(foundElement);
  if (element) {
    element.childWorkspaceId = workspaceId;
    await store.dispatch("updateSimpleElement", {
      simpleElement: element,
      undoable: true,
    });
  }

  return workspaceId;
};

export const deleteWorkspace = async (workspaceId, force) => {
  if (!force) {
    // Show confirmation dialog
    const confirmed = window.confirm(
      "Are you sure you want to delete this workspace? This action cannot be undone.",
    );
    if (!confirmed) return;
  }

  if (workspaceId === store.state.rootWorkspaceId) {
    console.error("Cannot delete root workspace");
    return;
  }

  // Find if any element has this workspace expanded and collapse it
  const workspaceElement = Array.from(
    store.state.allSimpleElements.values(),
  ).find((element) => element.childWorkspaceId === workspaceId);

  if (workspaceElement && isWorkspaceExpanded(workspaceElement.id)) {
    await collapseWorkspace(workspaceElement.id, true);
  }

  // Clean up any elements that belonged to this workspace
  for (const element of store.state.allSimpleElements.values()) {
    // Handle elements with parentWorkspaceIds (new format)
    if (
      element.parentWorkspaceIds &&
      element.parentWorkspaceIds.includes(workspaceId)
    ) {
      if (element.parentWorkspaceIds.length === 1) {
        // If this is the only parent workspace, delete the element
        await deleteElement({ elementId: element.id, force: force });
      } else {
        // If the element has multiple parent workspaces, just remove this one
        const updatedElement = deepClone(element);
        updatedElement.parentWorkspaceIds =
          updatedElement.parentWorkspaceIds.filter((id) => id !== workspaceId);
        await store.dispatch("updateSimpleElement", {
          simpleElement: updatedElement,
          undoable: true,
        });
      }
    }
  }

  // Clean up any segments that belonged to this workspace
  for (const segment of store.state.allSimpleSegments.values()) {
    if (segment.parentWorkspaceId === workspaceId) {
      await deleteSegment(segment.id, force);
    }
  }

  // Clean up any sequences that belonged to this workspace
  for (const sequence of store.state.sequences.values()) {
    if (sequence.parentWorkspaceId === workspaceId) {
      store.state.sequences.delete(sequence.id);
    }
  }

  // Delete the workspace
  if (store.state.currentWorkspaceId === workspaceId) {
    await enterWorkspace(store.state.workspaceIds[0]);
  }
  await store.dispatch("deleteWorkspaceAction", {
    workspaceId,
    undoable: true,
  });
};

export function deleteAllExpandedWorkspaces() {
  // Clear all expanded workspace boxes
  for (const element of store.state.allSimpleElements.values()) {
    if (isWorkspaceExpanded(element.id)) {
      const expandedParams = getExpandedWorkspaceParameters(element.id);
      if (expandedParams) {
        // Remove the expanded workspace box
        drawAdapter.removeExpandedWorkspaceBox(element.childWorkspaceId);
      }
    }
  }
}

export const enterWorkspace = async (workspaceId, undoable) => {
  // Stop any running animations
  await toggleCreationMode(false);
  forceStopAnimation();
  await unselectAllSegments();
  await unselectAllElements();
  deleteAllGraphicObjects();
  deleteAllExpandedWorkspaces();

  // If the workspace doesn't exists, fallback to root workspace
  if (!store.state.workspaceIds.includes(workspaceId)) {
    workspaceId = store.state.workspaceIds[0];
  }

  // Enter the workspace
  await store.dispatch("enterWorkspaceAction", { workspaceId, undoable });

  store.dispatch("updateLoadingAction", true).then();

  createGraphicElements(currentWorkspaceElements());

  // let's re-expand all elements that are supposed to be expanded
  for (const element of store.state.allSimpleElements.values()) {
    if (
      element.workspaceParameters[store.state.currentWorkspaceId]
        ?.expandedParameters
    ) {
      await expandWorkspace({
        elementId: element.id,
        pushElements: false,
        undoable: false,
      });
    }
  }

  // Select a sequence for the current workspace if available
  const workspaceSequences = currentWorkspaceSequences();
  if (workspaceSequences.length > 0) {
    await selectSequence(workspaceSequences[0].id);
  } else {
    // Clear current sequence if no sequences exist for this workspace
    await store.dispatch("updateCurrentSequenceIdAction", {
      sequenceId: null,
    });
  }

  await store.dispatch("updateLoadingAction", false).then();
};

/**
 * Expands a workspace in place, showing its contents within a dotted box
 * @param {string} elementId - The ID of the element containing the workspace to expand
 * @param {boolean} pushElements - Whether to push other elements away
 * @param {boolean} undoable - Whether this action should be undoable
 * @returns {Promise<void>}
 */
export const expandWorkspace = async ({
  elementId,
  pushElements,
  undoable = true,
}) => {
  // Get the element and its workspace
  const element = store.state.allSimpleElements.get(elementId);
  if (!element || !element.childWorkspaceId) {
    console.warn("Cannot expand workspace: element does not have a workspace");
    return;
  }

  const workspaceId = element.childWorkspaceId;

  // Get all elements in the child workspace
  const childElements = Array.from(
    store.state.allSimpleElements.values(),
  ).filter((el) => el.parentWorkspaceIds.includes(workspaceId));

  if (childElements.length === 0) {
    console.warn("Cannot expand empty workspace");
    return;
  }

  // Calculate the bounding box for all child elements
  const boundingBox = calculateBoundingBoxForElements(
    childElements,
    workspaceId,
  );

  // Add some padding
  const padding = 50;
  boundingBox.minX -= padding;
  boundingBox.minY -= padding;
  boundingBox.maxX += padding;
  boundingBox.maxY += padding;

  // Calculate width and height
  const width = boundingBox.maxX - boundingBox.minX;
  const height = boundingBox.maxY - boundingBox.minY;

  // Calculate center position
  const centerX = boundingBox.minX + width / 2;
  const centerY = boundingBox.minY + height / 2;

  // Get the original element's position
  const position = {
    x: element.workspaceParameters[store.state.currentWorkspaceId].x,
    y: element.workspaceParameters[store.state.currentWorkspaceId].y,
  };

  // Store expanded parameters in the element's workspaceParameters
  const updatedElement = deepClone(element);
  updatedElement.workspaceParameters[
    store.state.currentWorkspaceId
  ].expandedParameters = {
    width,
    height,
  };

  await store.dispatch("updateSimpleElement", {
    simpleElement: updatedElement,
    undoable,
  });

  // Create expanded workspace data for graphics operations
  const expandedData = {
    workspaceId,
    elementId,
    boundingBox,
    width,
    height,
    centerX,
    centerY,
    childElementIds: childElements.map((el) => el.id),
  };

  // Find all segments connected to this element
  const connectedSegments = Array.from(
    store.state.allSimpleSegments.values(),
  ).filter((segment) => {
    return (
      segment.segmentElementsInfo.element1 === elementId ||
      segment.segmentElementsInfo.element2 === elementId
    );
  });

  // Store the connected element IDs for later use
  const connectedElementIds = connectedSegments.map((segment) => {
    return segment.segmentElementsInfo.element1 === elementId
      ? segment.segmentElementsInfo.element2
      : segment.segmentElementsInfo.element1;
  });

  // Get the original element's dimensions
  const originalElementWidth =
    element.workspaceParameters[store.state.currentWorkspaceId].width;
  const originalElementHeight =
    element.workspaceParameters[store.state.currentWorkspaceId].height;

  // Hide (delete) the original element
  drawAdapter.deleteElement(elementId, true);

  // Create a dotted box to represent the expanded workspace
  const boxGroup = drawAdapter.createExpandedWorkspaceBoxWithChildElements(
    expandedData,
    childElements,
    position,
  );

  expandedData.centerX = boxGroup.box.cx();
  expandedData.centerY = boxGroup.box.cy();

  // Calculate the size difference between the original element and the expanded box
  const sizeDiffX = width - originalElementWidth;
  const sizeDiffY = height - originalElementHeight;

  // Find all elements in the current workspace that need to be moved
  // We'll move all elements around the expanded element based on their position
  const elementsToMove = Array.from(
    store.state.allSimpleElements.values(),
  ).filter((el) => {
    // Only consider elements in the current workspace
    if (!el.parentWorkspaceIds.includes(store.state.currentWorkspaceId)) {
      return false;
    }

    // Skip the element being expanded
    if (el.id === elementId) {
      return false;
    }

    // Check if this element is an expanded workspace
    const isExpandedWorkspace = isWorkspaceExpanded(el.id);

    // Skip non-expanded workspaces that don't have a graphic element
    const graphicElement = drawAdapter.parameters.allElements.get(el.id);
    if (!graphicElement && !isExpandedWorkspace) {
      return false;
    }

    // Check if this element is inside the workspace being expanded
    const isInsideExpandedWorkspace = expandedData.childElementIds.includes(
      el.id,
    );
    if (isInsideExpandedWorkspace) {
      return false;
    }

    // Check if this element is inside another expanded workspace (but not the expanded workspace itself)
    let isInsideOtherExpandedWorkspace = false;
    for (const otherElement of store.state.allSimpleElements.values()) {
      if (
        otherElement.id !== elementId &&
        isWorkspaceExpanded(otherElement.id)
      ) {
        // Get child elements of this expanded workspace
        const otherChildElements = Array.from(
          store.state.allSimpleElements.values(),
        ).filter((childEl) =>
          childEl.parentWorkspaceIds.includes(otherElement.childWorkspaceId),
        );

        if (otherChildElements.some((childEl) => childEl.id === el.id)) {
          isInsideOtherExpandedWorkspace = true;
          break;
        }
      }
    }

    // We want to move:
    // 1. Regular elements directly in the current workspace
    // 2. Expanded workspaces (they will move their contained elements)
    // We don't want to move:
    // 1. Elements inside the workspace being expanded
    // 2. Elements inside other expanded workspaces (they will move with their parent workspace)
    if (isInsideOtherExpandedWorkspace && !isExpandedWorkspace) {
      return false;
    }

    return true;
  });

  // Move the elements based on the size difference
  for (const el of elementsToMove) {
    const elPos = {
      x: el.workspaceParameters[store.state.currentWorkspaceId].x,
      y: el.workspaceParameters[store.state.currentWorkspaceId].y,
    };

    // Calculate the direction vector from the center of the expanded element to this element
    const dirX = elPos.x - position.x;
    const dirY = elPos.y - position.y;

    if (pushElements) {
      // Calculate push factors using our helper function
      const { pushX, pushY } = calculatePushFactors(
        dirX,
        dirY,
        sizeDiffX,
        sizeDiffY,
        true, // isExpanding = true
      );

      // Apply the calculated push
      let newX = elPos.x + pushX;
      let newY = elPos.y + pushY;

      // Track this push operation for proper collapse behavior
      await store.dispatch("addElementPushAction", {
        elementId: el.id,
        workspaceElementId: elementId,
        pushX,
        pushY,
        workspaceId: store.state.currentWorkspaceId,
      });

      // Check if this element is an expanded workspace
      const isExpandedWorkspace = isWorkspaceExpanded(el.id);
      if (isExpandedWorkspace) {
        // Move the expanded workspace box using draw-adapter
        const boxGroup = drawAdapter.moveExpandedWorkspaceBox(
          el.childWorkspaceId,
          newX,
          newY,
        );

        // Update the element's position in state
        await updateExpandedWorkspaceElementPosition(el.id, newX, newY, true);

        // In order to update the top left position. TODO: stop using graphic elements as reference!!
        drawAdapter.updateExpandedWorkspaceReferences(el.childWorkspaceId);

        // Retrace all segments connected to this expanded workspace
        if (boxGroup && boxGroup.expandedWorkspaceBox.destinationElementIds) {
          for (const destId of boxGroup.expandedWorkspaceBox
            .destinationElementIds) {
            traceSegment(el.id, destId, true);
          }
        }
      } else {
        // Move the regular element to the new position
        await moveElementToPosition(el.id, newX, newY, true);
      }
    }
  }

  const offsetPosition = {
    x: position.x - centerX,
    y: position.y - centerY,
  };

  // Trace segments for child elements
  childElements.forEach((childElement) => {
    retraceAllSegments(
      childElement.id,
      null,
      expandedData.workspaceId,
      offsetPosition,
    );
  });

  // Trace segments for the expanded box itself
  // This ensures connections to other elements are immediately visible
  for (const connectedElementId of connectedElementIds) {
    // Only trace if the connected element is not one of the child elements
    if (!childElements.some((child) => child.id === connectedElementId)) {
      traceSegment(elementId, connectedElementId, true);
    }
  }

  // Recreate the connections for the expanded box
  // For each segment that was connected to the original element
  for (const segment of connectedSegments) {
    const otherElementId =
      segment.segmentElementsInfo.element1 === elementId
        ? segment.segmentElementsInfo.element2
        : segment.segmentElementsInfo.element1;

    // Create a new segment between the expanded box and the other element
    createConnectionBetweenElements(elementId, otherElementId, true);
  }
};

/**
 * Collapses all currently expanded workspaces
 * @param {boolean} undoable - Whether this action should be undoable
 * @returns {Promise<void>}
 */
export const collapseAllExpandedWorkspaces = async (undoable = false) => {
  // Iterate through all elements and collapse expanded workspaces
  for (const element of store.state.allSimpleElements.values()) {
    if (isWorkspaceExpanded(element.id)) {
      await collapseWorkspace(element.id, undoable);
    }
  }
};

/**
 * Collapses an expanded workspace, hiding its contents and showing the original element
 * @param {string} elementId - The ID of the element containing the expanded workspace
 * @param {boolean} undoable - Whether this action should be undoable
 * @returns {Promise<void>}
 */
export const collapseWorkspace = async (elementId, undoable = true) => {
  // Check if the workspace is expanded
  if (!isWorkspaceExpanded(elementId)) {
    console.warn("Cannot collapse workspace: workspace is not expanded");
    return;
  }

  const element = store.state.allSimpleElements.get(elementId);

  // Find all segments connected to the expanded workspace box
  const connectedSegments = Array.from(
    store.state.allSimpleSegments.values(),
  ).filter((segment) => {
    return (
      segment.segmentElementsInfo.element1 === elementId ||
      segment.segmentElementsInfo.element2 === elementId
    );
  });

  // Store the connected element IDs to recreate connections later
  const connectedElementIds = connectedSegments.map((segment) => {
    return segment.segmentElementsInfo.element1 === elementId
      ? segment.segmentElementsInfo.element2
      : segment.segmentElementsInfo.element1;
  });

  // Delete the segments connected to the expanded box
  for (const segment of connectedSegments) {
    await deleteSegment(segment.id, true, true);
  }

  // Get child elements of the workspace being collapsed
  const childElements = Array.from(
    store.state.allSimpleElements.values(),
  ).filter((el) => el.parentWorkspaceIds.includes(element.childWorkspaceId));

  // Hide all child elements
  for (const childElement of childElements) {
    const graphicElement = drawAdapter.parameters.allElements.get(
      childElement.id,
    );
    if (graphicElement) {
      drawAdapter.deleteElement(childElement.id, true);
    }
  }

  // Get the expanded box and its dimensions
  const expandedBox = drawAdapter.parameters.expandedWorkspaceBoxes.get(
    element.childWorkspaceId,
  );

  // Get center coordinates of the expanded workspace box (not the group)
  const x = expandedBox.box.cx();
  const y = expandedBox.box.cy();

  // Remove the dotted box
  drawAdapter.removeExpandedWorkspaceBox(element.childWorkspaceId);

  // Delete the old graphic element
  drawAdapter.deleteElement(elementId, false);

  // Recreate the element
  createElementFromState(
    elementId,
    store.state.currentWorkspaceId,
    store.state.displayShadows,
  );
  await moveElementToPosition(elementId, x, y, false);
  const graphicElement = drawAdapter.parameters.allElements.get(elementId);
  await store.dispatch("updateElement", {
    element: graphicElement,
    undoable: false,
  });

  // Find all elements in the current workspace that need to be moved back
  // We'll move all elements around the collapsed element based on their position
  const elementsToMove = Array.from(
    store.state.allSimpleElements.values(),
  ).filter((el) => {
    // Only consider elements in the current workspace
    if (!el.parentWorkspaceIds.includes(store.state.currentWorkspaceId)) {
      return false;
    }

    // Skip the element being collapsed
    if (el.id === elementId) {
      return false;
    }

    // Check if this element is an expanded workspace
    const isExpandedWorkspace = isWorkspaceExpanded(el.id);

    // Skip elements that are not directly in the current workspace
    // This includes elements inside expanded workspaces (but not the expanded workspace itself)
    const graphicElement = drawAdapter.parameters.allElements.get(el.id);
    if (!graphicElement && !isExpandedWorkspace) {
      return false;
    }

    // Check if this element is inside the workspace being collapsed
    const isInsideExpandedWorkspace = childElements.some(
      (child) => child.id === el.id,
    );
    if (isInsideExpandedWorkspace) {
      return false;
    }

    // Check if this element is inside another expanded workspace (but not the expanded workspace itself)
    let isInsideOtherExpandedWorkspace = false;
    for (const otherElement of store.state.allSimpleElements.values()) {
      if (
        otherElement.id !== elementId &&
        isWorkspaceExpanded(otherElement.id)
      ) {
        // Get child elements of this expanded workspace
        const otherChildElements = Array.from(
          store.state.allSimpleElements.values(),
        ).filter((childEl) =>
          childEl.parentWorkspaceIds.includes(otherElement.childWorkspaceId),
        );

        if (otherChildElements.some((childEl) => childEl.id === el.id)) {
          isInsideOtherExpandedWorkspace = true;
          break;
        }
      }
    }

    // We want to move:
    // 1. Regular elements directly in the current workspace
    // 2. Expanded workspaces (they will move their contained elements)
    // We don't want to move:
    // 1. Elements inside the workspace being collapsed
    // 2. Elements inside other expanded workspaces (they will move with their parent workspace)
    if (isInsideOtherExpandedWorkspace && !isExpandedWorkspace) {
      return false;
    }

    // Move all elements except the one being collapsed and those inside expanded workspaces
    return true;
  });

  // Move the elements back by reversing their push history from this workspace
  for (const el of elementsToMove) {
    const pushHistory = store.state.elementPushHistory.get(el.id) || [];

    // Find the push from this workspace being collapsed
    const pushFromThisWorkspace = pushHistory.find(
      (push) =>
        push.workspaceElementId === elementId &&
        push.workspaceId === store.state.currentWorkspaceId,
    );

    if (!pushFromThisWorkspace) {
      // Element wasn't moved by this workspace expansion, skip it
      continue;
    }

    const elPos = {
      x: el.workspaceParameters[store.state.currentWorkspaceId].x,
      y: el.workspaceParameters[store.state.currentWorkspaceId].y,
    };

    // Reverse the push operation
    let newX = elPos.x - pushFromThisWorkspace.pushX;
    let newY = elPos.y - pushFromThisWorkspace.pushY;

    // Check if this element is an expanded workspace
    const isExpandedWorkspace = isWorkspaceExpanded(el.id);
    if (isExpandedWorkspace) {
      // Move the expanded workspace box using draw-adapter
      drawAdapter.moveExpandedWorkspaceBox(el.childWorkspaceId, newX, newY);

      // Update the element's position in state
      await updateExpandedWorkspaceElementPosition(el.id, newX, newY, false);

      // In order to update the top left position. TODO: stop using graphic elements as reference!!
      drawAdapter.updateExpandedWorkspaceReferences(el.childWorkspaceId);

      // Retrace all segments connected to this element
      retraceAllSegments(el.id);
    } else {
      // Move the regular element to the new position
      await moveElementToPosition(el.id, newX, newY, false);
    }
  }

  // Remove push history entries for this workspace from all elements
  await store.dispatch("removeElementPushesForWorkspaceAction", {
    workspaceElementId: elementId,
  });

  // Remove the expanded parameters from the element
  const updatedElement = deepClone(element);
  delete updatedElement.workspaceParameters[store.state.currentWorkspaceId]
    .expandedParameters;
  await store.dispatch("updateSimpleElement", {
    simpleElement: updatedElement,
    undoable,
  });

  // Recreate the connections for the original element
  for (const connectedElementId of connectedElementIds) {
    createConnectionBetweenElements(elementId, connectedElementId, true);
  }

  // Make sure all segments are properly traced
  // This ensures connections are immediately visible
  for (const connectedElementId of connectedElementIds) {
    traceSegment(elementId, connectedElementId, true);
  }
};

/**
 * Resizes an expanded workspace box and all its child elements proportionally (visual only)
 * @param {string} elementId - The ID of the element containing the expanded workspace
 * @param {number} newWidth - The new width of the expanded box
 * @param {number} newHeight - The new height of the expanded box
 * @returns {Promise<void>}
 */
export const resizeExpandedWorkspace = async (
  elementId,
  newWidth,
  newHeight,
) => {
  // Check if the workspace is expanded
  if (!isWorkspaceExpanded(elementId)) {
    console.warn("Cannot resize workspace: workspace is not expanded");
    return;
  }

  const element = store.state.allSimpleElements.get(elementId);

  // Get child elements for the workspace
  const childElements = Array.from(
    store.state.allSimpleElements.values(),
  ).filter((el) => el.parentWorkspaceIds.includes(element.childWorkspaceId));

  // Resize the expanded workspace box
  const {
    boxGroup,
    scaleX,
    scaleY,
    centerX,
    centerY,
    initialCenterX,
    initialCenterY,
  } = drawAdapter.resizeExpandedWorkspaceBox(
    element.childWorkspaceId,
    newWidth,
    newHeight,
  );

  if (!boxGroup) {
    console.error("Failed to resize expanded workspace box");
    return;
  }

  // Update the expanded parameters in the element
  const updatedElement = deepClone(element);
  updatedElement.workspaceParameters[
    store.state.currentWorkspaceId
  ].expandedParameters = {
    width: newWidth,
    height: newHeight,
  };

  await store.dispatch("updateSimpleElement", {
    simpleElement: updatedElement,
    undoable: true,
  });

  // Resize and reposition all child elements proportionally (visual only)
  for (const childElement of childElements) {
    const childElementId = childElement.id;
    if (!childElement) continue;

    // Get the child element's graphic element
    const graphicElement =
      drawAdapter.parameters.allElements.get(childElementId);
    if (!graphicElement) continue;

    const entity = graphicElement.graphicElement.entity;

    // Get initial positions from captured data, or current positions if not captured
    const expandedWorkspaceBox = boxGroup.expandedWorkspaceBox;
    let currentX, currentY, currentWidth, currentHeight;

    if (
      expandedWorkspaceBox.initialChildPositions &&
      expandedWorkspaceBox.initialChildPositions.has(childElementId)
    ) {
      // Use captured initial positions (during drag)
      const initialPos =
        expandedWorkspaceBox.initialChildPositions.get(childElementId);
      currentX = initialPos.x;
      currentY = initialPos.y;
      currentWidth = initialPos.width;
      currentHeight = initialPos.height;
    } else {
      // Use current positions (fallback or when not dragging)
      currentX = entity.cx();
      currentY = entity.cy();
      currentWidth = entity.width();
      currentHeight = entity.height();
    }

    // Calculate the relative position from the initial center of the expanded box
    const relX = currentX - initialCenterX;
    const relY = currentY - initialCenterY;

    // Scale the relative position
    const newRelX = relX * scaleX;
    const newRelY = relY * scaleY;

    // Calculate the new absolute position based on current center
    const newX = centerX + newRelX;
    const newY = centerY + newRelY;

    // Calculate the new width and height based on initial size
    const newChildWidth = currentWidth * scaleX;
    const newChildHeight = currentHeight * scaleY;

    // Update the graphic element (visual only, don't touch store)
    // Scale and position the graphic element
    entity.width(newChildWidth).height(newChildHeight);
    entity.move(newX - newChildWidth / 2, newY - newChildHeight / 2);

    // Update dummy elements
    if (graphicElement.graphicElement.dummy) {
      graphicElement.graphicElement.dummy
        .width(newChildWidth)
        .height(newChildHeight);
      graphicElement.graphicElement.dummy.center(newX, newY);
    }
    if (graphicElement.graphicElement.dummyStroke) {
      graphicElement.graphicElement.dummyStroke
        .width(newChildWidth)
        .height(newChildHeight);
      graphicElement.graphicElement.dummyStroke.center(newX, newY);
    }

    // Update element name if it exists
    if (graphicElement.graphicElement.elementName) {
      graphicElement.graphicElement.elementName.width(newChildWidth - 10);
      graphicElement.graphicElement.elementName.center(newX, newY);
    }

    // Update resizer position
    if (graphicElement.graphicElement.resizer) {
      graphicElement.graphicElement.resizer.move(
        newX + newChildWidth / 2 - 15,
        newY + newChildHeight / 2 - 15,
      );
    }

    // Update anchors and bring them to front
    if (entity.topAnchor) {
      entity.topAnchor.center(newX, newY - newChildHeight / 2);
      entity.topAnchor.front();
    }
    if (entity.bottomAnchor) {
      entity.bottomAnchor.center(newX, newY + newChildHeight / 2);
      entity.bottomAnchor.front();
    }
    if (entity.leftAnchor) {
      entity.leftAnchor.center(newX - newChildWidth / 2, newY);
      entity.leftAnchor.front();
    }
    if (entity.rightAnchor) {
      entity.rightAnchor.center(newX + newChildWidth / 2, newY);
      entity.rightAnchor.front();
    }

    // Re-add the entire element group to the expanded box group to ensure it moves with the box
    // and maintains its internal structure
    if (!boxGroup.has(graphicElement.graphicElement)) {
      boxGroup.add(graphicElement.graphicElement);
    }
  }

  // Retrace all segments within the expanded workspace and add them to the group
  retraceSegmentsInExpandedWorkspace(
    element.childWorkspaceId,
    childElements.map((el) => el.id),
    boxGroup,
  );
};

/**
 * Retraces all segments between elements within an expanded workspace
 * @param {string} workspaceId - The ID of the expanded workspace
 * @param {Array} childElementIds - Array of child element IDs in the workspace
 * @param {Object} boxGroup - The expanded workspace box group to add segments to
 */
export const retraceSegmentsInExpandedWorkspace = (
  workspaceId,
  childElementIds,
  boxGroup = null,
) => {
  // Find all segments that connect elements within this expanded workspace
  const segmentsToRetrace = [];

  for (const segment of store.state.allSimpleSegments.values()) {
    if (segment.parentWorkspaceId.includes(workspaceId)) {
      const sourceId = segment.segmentElementsInfo.element1;
      const destId = segment.segmentElementsInfo.element2;

      // Check if both elements are child elements of this expanded workspace
      if (
        childElementIds.includes(sourceId) &&
        childElementIds.includes(destId)
      ) {
        segmentsToRetrace.push(segment);
      }
    }
  }

  // Retrace each segment
  for (const segment of segmentsToRetrace) {
    const sourceId = segment.segmentElementsInfo.element1;
    const destId = segment.segmentElementsInfo.element2;

    // Use drawAdapter to retrace the segment directly
    const sourceElement = drawAdapter.parameters.allElements.get(sourceId);
    const destElement = drawAdapter.parameters.allElements.get(destId);

    if (sourceElement && destElement) {
      // Find connected anchors based on current positions
      const connectedAnchors = findConnectedAnchorsFromGraphicElements(
        sourceElement.graphicElement,
        destElement.graphicElement,
      );

      if (connectedAnchors) {
        drawAdapter.traceSegment(
          segment.id,
          segment.segmentElementsInfo.mode,
          connectedAnchors,
        );

        // Add the segment to the expanded box group if provided
        if (boxGroup) {
          const segmentGraphic = drawAdapter.parameters.allSegments.get(
            segment.id,
          );
          if (segmentGraphic && segmentGraphic.graphicSegment) {
            if (!boxGroup.has(segmentGraphic.graphicSegment)) {
              segmentGraphic.graphicSegment.off();
              boxGroup.add(segmentGraphic.graphicSegment);
            }
          }
        }
      }
    }
  }
};

/**
 * Finds connected anchors between two graphic elements based on their current positions
 * @param {Object} sourceGraphicElement - The source graphic element
 * @param {Object} destGraphicElement - The destination graphic element
 * @returns {Object} The connected anchors object
 */
const findConnectedAnchorsFromGraphicElements = (
  sourceGraphicElement,
  destGraphicElement,
) => {
  const sourceEntity = sourceGraphicElement.entity;
  const destEntity = destGraphicElement.entity;

  // Get center positions
  const sourceCx = sourceEntity.cx();
  const sourceCy = sourceEntity.cy();
  const destCx = destEntity.cx();
  const destCy = destEntity.cy();

  // Calculate direction vector
  const dx = destCx - sourceCx;
  const dy = destCy - sourceCy;

  // Determine best anchors based on direction
  let sourceAnchor, destAnchor;

  if (Math.abs(dx) > Math.abs(dy)) {
    // Horizontal connection
    if (dx > 0) {
      // Source to right of dest
      sourceAnchor = {
        x: sourceEntity.x() + sourceEntity.width(),
        y: sourceCy,
      };
      destAnchor = { x: destEntity.x(), y: destCy };
    } else {
      // Source to left of dest
      sourceAnchor = { x: sourceEntity.x(), y: sourceCy };
      destAnchor = { x: destEntity.x() + destEntity.width(), y: destCy };
    }
  } else {
    // Vertical connection
    if (dy > 0) {
      // Source above dest
      sourceAnchor = {
        x: sourceCx,
        y: sourceEntity.y() + sourceEntity.height(),
      };
      destAnchor = { x: destCx, y: destEntity.y() };
    } else {
      // Source below dest
      sourceAnchor = { x: sourceCx, y: sourceEntity.y() };
      destAnchor = { x: destCx, y: destEntity.y() + destEntity.height() };
    }
  }

  return {
    anchorFrom: sourceAnchor,
    anchorTo: destAnchor,
  };
};

/**
 * Calculates push factors for element positioning when expanding/collapsing workspaces
 * @param {number} dirX - X direction from center of expanded/collapsed element to the element
 * @param {number} dirY - Y direction from center of expanded/collapsed element to the element
 * @param {number} sizeDiffX - Width difference between expanded and collapsed states
 * @param {number} sizeDiffY - Height difference between expanded and collapsed states
 * @param {boolean} isExpanding - True if expanding, false if collapsing
 * @returns {Object} - The push factors { pushX, pushY }
 */
export const calculatePushFactors = (
  dirX,
  dirY,
  sizeDiffX,
  sizeDiffY,
  isExpanding,
) => {
  // Calculate the distance between the centers
  const distance = Math.sqrt(dirX * dirX + dirY * dirY);

  // Return zero push if the element is at the exact same position (avoid division by zero)
  if (distance === 0) return { pushX: 0, pushY: 0 };

  // Normalize the direction vector
  const normalizedDirX = dirX / distance;
  const normalizedDirY = dirY / distance;

  // Calculate alignment factors (how aligned the elements are horizontally or vertically)
  // Values close to 1 mean well-aligned, values close to 0 mean not aligned
  const horizontalAlignmentFactor = Math.abs(normalizedDirY);
  const verticalAlignmentFactor = Math.abs(normalizedDirX);

  // Threshold for considering elements "aligned"
  const alignmentThreshold = 0.2; // Lower values mean stricter alignment detection

  let pushX = 0;
  let pushY = 0;

  // Direction multiplier: 1 for expanding, -1 for collapsing
  const dirMultiplier = isExpanding ? 1 : -1;

  // If elements are more horizontally aligned (one above the other)
  if (horizontalAlignmentFactor > 1 - alignmentThreshold) {
    // Prioritize vertical movement to maintain horizontal alignment
    pushY = (dirMultiplier * Math.sign(dirY) * sizeDiffY) / 2;

    // Add minimal horizontal push if needed
    if (Math.abs(dirX) > 0.001) {
      pushX =
        ((dirMultiplier * Math.sign(dirX) * sizeDiffX) / 2) *
        (1 - horizontalAlignmentFactor);
    }
  }
  // If elements are more vertically aligned (side by side)
  else if (verticalAlignmentFactor > 1 - alignmentThreshold) {
    // Prioritize horizontal movement to maintain vertical alignment
    pushX = (dirMultiplier * Math.sign(dirX) * sizeDiffX) / 2;

    // Add minimal vertical push if needed
    if (Math.abs(dirY) > 0.001) {
      pushY =
        ((dirMultiplier * Math.sign(dirY) * sizeDiffY) / 2) *
        (1 - verticalAlignmentFactor);
    }
  }
  // For elements that aren't particularly aligned in either direction
  else {
    // Use a weighted approach based on the direction vector
    pushX =
      ((dirMultiplier * Math.sign(dirX) * sizeDiffX) / 2) *
      Math.abs(normalizedDirX);
    pushY =
      ((dirMultiplier * Math.sign(dirY) * sizeDiffY) / 2) *
      Math.abs(normalizedDirY);
  }

  return { pushX, pushY };
};

/**
 * Calculates the bounding box for a set of elements
 * @param {Array} elements - Array of elements to calculate bounding box for
 * @param {string} workspaceId - The ID of the workspace the elements belong to
 * @returns {Object} - The bounding box with minX, minY, maxX, maxY
 */
export const calculateBoundingBoxForElements = (elements, workspaceId) => {
  if (!elements || elements.length === 0) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const element of elements) {
    const params = element.workspaceParameters[workspaceId];
    if (!params) continue;

    const x = params.x;
    const y = params.y;
    const width = params.width || 100;
    const height = params.height || 100;

    // Calculate element bounds
    const elementMinX = x - width / 2;
    const elementMinY = y - height / 2;
    const elementMaxX = x + width / 2;
    const elementMaxY = y + height / 2;

    // Update bounding box
    minX = Math.min(minX, elementMinX);
    minY = Math.min(minY, elementMinY);
    maxX = Math.max(maxX, elementMaxX);
    maxY = Math.max(maxY, elementMaxY);
  }

  return { minX, minY, maxX, maxY };
};

export const enterWorkspaceWithZoom = async (workspaceId, elementId) => {
  zoomToElement(elementId);
  await sleep(600);
  await enterWorkspace(workspaceId, true);
  draw.viewbox(0, 0, 50000, 50000);
  draw
    .animate({ duration: 300, when: "now" })
    .viewbox(2500, 2500, 10000, 10000);
};

export const enterWorkspaceWithZoomOut = async (workspaceId) => {
  if (!workspaceId) {
    return;
  }
  const currentWorkspaceId = store.state.currentWorkspaceId;

  // find center of screen
  const { centerX, centerY } = getViewCenterCoordinates();
  // find center of parent element

  const parentElement = Array.from(store.state.allSimpleElements.values()).find(
    (element) => element.childWorkspaceId === currentWorkspaceId,
  );

  draw
    .animate({ duration: 400, when: "now" })
    .zoom(0.1, { x: centerX, y: centerY });
  await sleep(400);
  draw.zoom(20, {
    x:
      parentElement.workspaceParameters[parentElement.parentWorkspaceIds[0]]
        .x || centerX,
    y:
      parentElement.workspaceParameters[parentElement.parentWorkspaceIds[0]]
        .y || centerY,
  });
  await enterWorkspace(workspaceId, true);

  const viewboxCoordinates =
    getViewboxSurroundingAllElementsForWorkspace(workspaceId);

  if (viewboxCoordinates === null) {
    draw.animate(600).zoom(1, { x: centerX, y: centerY });
  } else {
    draw
      .animate({ duration: 600, when: "now" })
      .viewbox(
        viewboxCoordinates.newViewboxX,
        viewboxCoordinates.newViewboxY,
        viewboxCoordinates.screenWidth,
        viewboxCoordinates.screenHeight,
      );
  }
};

export const getViewboxSurroundingAllElementsForWorkspace = (workspaceId) => {
  const { furtherLeftX, furtherRightX, furtherTopY, furtherBottomY } =
    getViewAllElementsWorkspaceCoordinates(workspaceId);

  if (
    furtherLeftX === null ||
    furtherRightX === null ||
    furtherTopY === null ||
    furtherBottomY === null
  ) {
    return null;
  }

  let width = furtherRightX - furtherLeftX;
  let screenWidth = width * (10000 / window.innerWidth);
  let screenHeight = screenWidth;
  let height = screenHeight * (window.innerHeight / 10000);
  let newViewboxX = furtherLeftX;
  let newViewboxY = furtherTopY - (height - (furtherBottomY - furtherTopY)) / 2;

  if (height < furtherBottomY - furtherTopY) {
    height = furtherBottomY - furtherTopY;
    screenHeight = height * (10000 / window.innerHeight);
    screenWidth = screenHeight;
    width = screenWidth * (window.innerWidth / 10000);
    newViewboxX = furtherLeftX - (width - (furtherRightX - furtherLeftX)) / 2;
    newViewboxY = furtherTopY;
  }
  return { screenWidth, screenHeight, newViewboxX, newViewboxY };
};

export const getViewCenterCoordinates = () => {
  const windowWith = window.innerWidth;
  const windowHeight = window.innerHeight;
  const viewX = draw.viewbox().x;
  const viewY = draw.viewbox().y;
  const viewWidth = draw.viewbox().width;
  const viewHeight = draw.viewbox().height;

  const centerX = viewX + (windowWith * (viewWidth / 10000)) / 2;
  const centerY = viewY + (windowHeight * (viewHeight / 10000)) / 2;

  return { centerX, centerY };
};

const getViewAllElementsWorkspaceCoordinates = (workspaceId) => {
  const elementsInWorkspace = Array.from(
    store.state.allSimpleElements.values(),
  ).filter((element) => {
    return element.parentWorkspaceIds.includes(workspaceId);
  });
  if (isEmptyObject(elementsInWorkspace)) {
    return {
      furtherLeftX: null,
      furtherRightX: null,
      furtherTopY: null,
      furtherBottomY: null,
    };
  }
  const furtherLeftX =
    elementsInWorkspace.reduce((acc, element) => {
      // Get position from workspaceParameters if available
      let x = element.workspaceParameters[element.parentWorkspaceIds[0]].x;
      if (
        element.workspaceParameters &&
        element.workspaceParameters[workspaceId]
      ) {
        x = element.workspaceParameters[workspaceId].x;
      }
      return Math.min(
        acc,
        x - element.workspaceParameters[workspaceId].width / 2,
      );
    }, Infinity) - 100;
  const furtherRightX =
    elementsInWorkspace.reduce((acc, element) => {
      // Get position from workspaceParameters if available
      let x = element.workspaceParameters[element.parentWorkspaceIds[0]].x;
      if (
        element.workspaceParameters &&
        element.workspaceParameters[workspaceId]
      ) {
        x = element.workspaceParameters[workspaceId].x;
      }
      return Math.max(
        acc,
        x + element.workspaceParameters[workspaceId].width / 2,
      );
    }, 0) + 100;
  const furtherTopY =
    elementsInWorkspace.reduce((acc, element) => {
      // Get position from workspaceParameters if available
      let y = element.y;
      if (
        element.workspaceParameters &&
        element.workspaceParameters[workspaceId]
      ) {
        y = element.workspaceParameters[workspaceId].y;
      }
      return Math.min(
        acc,
        y - element.workspaceParameters[workspaceId].height / 2,
      );
    }, Infinity) - 100;
  const furtherBottomY =
    elementsInWorkspace.reduce((acc, element) => {
      // Get position from workspaceParameters if available
      let y = element.y;
      if (
        element.workspaceParameters &&
        element.workspaceParameters[workspaceId]
      ) {
        y = element.workspaceParameters[workspaceId].y;
      }
      return Math.max(
        acc,
        y + element.workspaceParameters[workspaceId].height / 2,
      );
    }, 0) + 100;
  return { furtherLeftX, furtherRightX, furtherTopY, furtherBottomY };
};

const createGraphicElements = (elements) => {
  const displayShadows = store.state.displayShadows;
  for (const simpleElement of elements.values()) {
    const workspaceParams =
      simpleElement.workspaceParameters[store.state.currentWorkspaceId];

    // Calculate absolute positions for drawing
    const absoluteAnchors = getAbsoluteAnchors(
      workspaceParams.anchors,
      workspaceParams.x,
      workspaceParams.y,
    );
    const absoluteTextX = workspaceParams.x + workspaceParams.textX;
    const absoluteTextY = workspaceParams.y + workspaceParams.textY;

    drawAdapter.createElement(
      workspaceParams.x,
      workspaceParams.y,
      simpleElement.type,
      simpleElement.name,
      absoluteTextX,
      absoluteTextY,
      simpleElement.id,
      simpleElement.color,
      simpleElement.textColor,
      simpleElement.textSize,
      simpleElement.textFont,
      simpleElement.textWeight,
      simpleElement.textAlign,
      workspaceParams.width,
      workspaceParams.height,
      absoluteAnchors,
      displayShadows,
    );
    retraceAllSegments(simpleElement.id);
  }
};

const currentWorkspaceElements = () => {
  return Array.from(store.state.allSimpleElements.values()).filter(
    (element) => {
      return (
        element.parentWorkspaceIds &&
        element.parentWorkspaceIds.includes(store.state.currentWorkspaceId)
      );
    },
  );
};

const currentWorkspaceSegments = () => {
  return Array.from(store.state.allSimpleSegments.values()).filter(
    (segment) => {
      return (
        segment.parentWorkspaceId &&
        segment.parentWorkspaceId.includes(store.state.currentWorkspaceId)
      );
    },
  );
};

const currentWorkspaceSequences = () => {
  return Array.from(store.state.sequences.values()).filter((sequence) => {
    return (
      sequence.parentWorkspaceId &&
      sequence.parentWorkspaceId.includes(store.state.currentWorkspaceId)
    );
  });
};

export const removeExpandIconsAndMenus = () => {
  elementIdsAvailable().forEach((simpleElementId) => {
    const simpleElement = store.state.allSimpleElements.get(simpleElementId);
    const element = drawAdapter.parameters.allElements.get(simpleElement.id);
    if (element.graphicElement) {
      // Remove hover helper
      element.graphicElement.hoverHelper?.remove();
      element.graphicElement.hoverHelper = null;

      // Remove expand icon and its components
      element.graphicElement.expandIconGroup?.remove();
      element.graphicElement.expandIcon = null;
      element.graphicElement.expandIconBg = null;
      element.graphicElement.expandIconDot1 = null;
      element.graphicElement.expandIconDot2 = null;
      element.graphicElement.expandIconDot3 = null;

      // Remove workspace expand button and its components
      element.graphicElement.workspaceExpandBtnGroup?.remove();
      element.graphicElement.workspaceExpandBtn = null;
      element.graphicElement.workspaceExpandBtnBg = null;
      element.graphicElement.workspaceExpandBtnGroup = null;

      // Remove old expand/collapse button from menu (if it exists)
      element.graphicElement.expandCollapseBtn?.remove();
      element.graphicElement.expandCollapseBtn = null;
    }
  });
};

/**
 * Adds a workspace element to the current workspace.
 * This creates a new element in the current workspace that references the workspace to add.
 * @param {string} workspaceToAddId - The ID of the workspace to add to the current workspace
 * @returns {Promise<boolean>} - True if the workspace was added successfully, false otherwise
 */
export const addWorkspaceToCurrentWorkspace = async (workspaceToAddId) => {
  // Find the element that has this workspace as its childWorkspaceId
  const workspaceElement = Array.from(
    store.state.allSimpleElements.values(),
  ).find((element) => element.childWorkspaceId === workspaceToAddId);

  if (!workspaceElement) {
    console.error(`No element found with childWorkspaceId ${workspaceToAddId}`);
    return false;
  }

  // currently we don't support adding the same workspace multiple times
  if (
    workspaceElement.parentWorkspaceIds.includes(store.state.currentWorkspaceId)
  ) {
    return false;
  }

  // Get the center of the current view
  const viewbox = draw.viewbox();
  const centerX = viewbox.x + ((window.innerWidth / 10000) * viewbox.width) / 2;
  const centerY =
    viewbox.y + ((window.innerHeight / 10000) * viewbox.height) / 2;
  // Create anchors for the new workspace position
  const anchors = createAnchors(centerX, centerY, 100, 100);

  // Set the workspace parameters for the current workspace
  workspaceElement.workspaceParameters[store.state.currentWorkspaceId] = {
    x: centerX,
    y: centerY,
    width: 100,
    height: 100,
    anchors: anchors,
    textX: -100 / 2 + 10, // Store as relative position
    textY: 0, // Store as relative position
    destinationElementIds: [],
  };

  // Calculate absolute positions for drawing
  const workspaceParams =
    workspaceElement.workspaceParameters[store.state.currentWorkspaceId];
  const absoluteAnchors = getAbsoluteAnchors(anchors, centerX, centerY);
  const absoluteTextX = centerX + workspaceParams.textX;
  const absoluteTextY = centerY + workspaceParams.textY;

  drawAdapter.createElement(
    centerX,
    centerY,
    workspaceElement.type,
    workspaceElement.name,
    absoluteTextX,
    absoluteTextY,
    workspaceElement.id,
    workspaceElement.color,
    workspaceElement.textColor,
    workspaceElement.textSize,
    workspaceElement.textFont,
    workspaceElement.textWeight,
    workspaceElement.textAlign,
    workspaceParams.width,
    workspaceParams.height,
    absoluteAnchors,
    store.state.displayShadows,
  );

  workspaceElement.parentWorkspaceIds.push(store.state.currentWorkspaceId);
  store.dispatch("updateSimpleElement", {
    simpleElement: workspaceElement,
    undoable: true,
  });
  return true;
};

/**
 * Remove connections between the given element and all other elements in the current workspace
 * @param {string} elementId - The ID of the element to remove connections from
 * @returns {Promise<void>}
 */
export const removeElementConnections = async (elementId) => {
  const element = store.state.allSimpleElements.get(elementId);
  if (!element) return;

  const currentWorkspaceId = store.state.currentWorkspaceId;
  const connectedElementIds =
    element.workspaceParameters[currentWorkspaceId].destinationElementIds;

  // Find and delete all segments connecting this element to others
  const allSegments = store.state.allSimpleSegments;
  for (const segment of Array.from(allSegments.values()).filter(
    (segment) =>
      (segment.segmentElementsInfo.element1 === elementId &&
        connectedElementIds.includes(segment.segmentElementsInfo.element2)) ||
      (segment.segmentElementsInfo.element2 === elementId &&
        connectedElementIds.includes(segment.segmentElementsInfo.element1)),
  )) {
    await deleteSegment(segment.id, true, true);
  }

  // Update the element's destinationElementIds
  const updatedElement = deepClone(element);
  updatedElement.workspaceParameters[currentWorkspaceId].destinationElementIds =
    [];
  await store.dispatch("updateSimpleElement", {
    simpleElement: updatedElement,
    undoable: true,
  });

  // Update connected elements to remove this element from their destinationElementIds
  for (const connectedId of connectedElementIds) {
    const connectedElement = store.state.allSimpleElements.get(connectedId);
    if (connectedElement) {
      const updatedConnectedElement = deepClone(connectedElement);
      const destIds =
        updatedConnectedElement.workspaceParameters[currentWorkspaceId]
          .destinationElementIds;
      const index = destIds.indexOf(elementId);
      if (index !== -1) {
        destIds.splice(index, 1);
        await store.dispatch("updateSimpleElement", {
          simpleElement: updatedConnectedElement,
          undoable: true,
        });
      }
    }
  }
};

// Function to show the move element dialog and get user decision
const showMoveElementDialog = (connectedCount) => {
  return new Promise((resolve) => {
    // Create a custom event with the connected count and resolve function
    const event = new CustomEvent("showMoveElementDialog", {
      detail: {
        connectedCount,
        resolve,
      },
    });

    // Dispatch the event to show the dialog
    document.dispatchEvent(event);
  });
};

export const moveElementToWorkspace = async (
  elementId,
  targetWorkspaceId,
  moveConnectedElements = null,
  getTargetWorkspaceIdCallback = null,
  overrideDecision = null,
) => {
  // Get the element from the store
  const element = store.state.allSimpleElements.get(elementId);
  const sequence = store.state.sequences.get(store.state.currentSequenceId);
  if (!element) {
    console.error(`Element with ID ${elementId} not found`);
    return false;
  }

  // If targetWorkspaceId is null and we have a callback, we're in the special case
  // where we need to get the workspace ID from the callback after user confirmation
  const isCreateNewWorkspace =
    targetWorkspaceId === null && getTargetWorkspaceIdCallback !== null;

  // If we're not creating a new workspace, check if the element already exists in the target workspace
  if (
    !isCreateNewWorkspace &&
    element.parentWorkspaceIds.includes(targetWorkspaceId)
  ) {
    return false;
  }

  // Check if the element has connections to other elements
  const currentWorkspaceId = store.state.currentWorkspaceId;
  const connectedElementIds =
    element.workspaceParameters[currentWorkspaceId].destinationElementIds;

  // If there are connected elements and moveConnectedElements is null (not specified),
  // ask the user what to do using our custom dialog
  if (connectedElementIds.length > 0 && moveConnectedElements === null) {
    try {
      // Show the dialog and wait for user decision
      const decision =
        overrideDecision ||
        (await showMoveElementDialog(connectedElementIds.length));

      if (decision === "moveAll") {
        // User chose to move all connected elements
        moveConnectedElements = true;
      } else if (decision === "moveSelected") {
        // User chose to remove connections and move only this element
        moveConnectedElements = false;
      } else if (decision === "cancel") {
        // User chose to cancel the operation
        return false; // Exit the function without doing anything
      }
    } catch (error) {
      console.error("Error showing move element dialog:", error);
      return false; // Exit if there was an error
    }
  }

  // If we're creating a new workspace, get the target workspace ID now
  // This ensures we only create the new workspace if the user confirms the move
  if (isCreateNewWorkspace) {
    targetWorkspaceId = await getTargetWorkspaceIdCallback();
    if (!targetWorkspaceId) {
      console.error("Failed to create new workspace");
      return false;
    }
  }

  // Handle sequence actions for the moved element
  await handleSequenceActionsForMovedElement(
    elementId,
    currentWorkspaceId,
    targetWorkspaceId,
  );

  // If there are connected elements and user chose to move them too
  if (connectedElementIds.length > 0 && moveConnectedElements === true) {
    // First move the current element
    await moveElementWithoutConnections(elementId, targetWorkspaceId);

    // Then move all connected elements
    for (const connectedId of connectedElementIds) {
      // Handle sequence actions for the connected element
      await handleSequenceActionsForMovedElement(
        connectedId,
        currentWorkspaceId,
        targetWorkspaceId,
      );

      // Recursive call with moveConnectedElements=true to ensure we move all connections
      // but we don't ask again (avoid multiple prompts)
      await moveElementToWorkspace(connectedId, targetWorkspaceId, true);
    }

    // update all segments from connected elements to the new workspace
    for (const segment of currentWorkspaceSegments().filter(
      (segment) =>
        (segment.segmentElementsInfo.element1 === elementId &&
          connectedElementIds.includes(segment.segmentElementsInfo.element2)) ||
        (segment.segmentElementsInfo.element2 === elementId &&
          connectedElementIds.includes(segment.segmentElementsInfo.element1)),
    )) {
      const foundSegment = deepClone(segment);
      // remove the old workspace
      foundSegment.parentWorkspaceId.splice(
        foundSegment.parentWorkspaceId.indexOf(currentWorkspaceId),
        1,
      );
      // add the new workspace
      foundSegment.parentWorkspaceId.push(targetWorkspaceId);
      await store.dispatch("updateSegmentAction", {
        segment: foundSegment,
        undoable: true,
      });
    }
    cleanEmptyFrames(sequence);
    return true;
  }

  // If there are connected elements but user chose not to move them
  if (connectedElementIds.length > 0 && moveConnectedElements === false) {
    // We don't need to do anything special here since this element is being moved alone
    // and all its connections should be removed
    await removeElementConnections(elementId);
  }

  // Move the element without its connections
  await moveElementWithoutConnections(elementId, targetWorkspaceId);
  cleanEmptyFrames(sequence);
  return true;
};

/**
 * Helper function to move an element to another workspace without handling connections
 * @param {string} elementId - The ID of the element to move
 * @param {string} targetWorkspaceId - The ID of the workspace to move the element to
 * @returns {Promise<void>}
 */
export const moveElementWithoutConnections = async (
  elementId,
  targetWorkspaceId,
) => {
  // Get the element from the store
  const element = store.state.allSimpleElements.get(elementId);
  if (!element) {
    console.error(`Element with ID ${elementId} not found`);
    return;
  }

  // Get the current workspace ID
  const currentWorkspaceId = store.state.currentWorkspaceId;

  // Delete the graphic element from the current workspace
  // Note: We're not using deleteElement because we don't want to remove it from the store
  drawAdapter.deleteElement(elementId, true);

  // Delete graphic segments connected to this element
  // Note: We're only deleting the graphic segments, not the segment data in the store
  const allSegments = store.state.allSimpleSegments;
  for (const segment of Array.from(allSegments.values()).filter(
    (segment) =>
      segment.segmentElementsInfo.element1 === elementId ||
      segment.segmentElementsInfo.element2 === elementId,
  )) {
    // Only delete the graphic segment, not the segment data
    drawAdapter.deleteSegment(segment.id);
  }

  // Update the element's parentWorkspaceIds
  const updatedElement = deepClone(element);

  // Remove the current workspace from parentWorkspaceIds
  const index = updatedElement.parentWorkspaceIds.findIndex(
    (id) => id === currentWorkspaceId,
  );
  if (index !== -1) {
    updatedElement.parentWorkspaceIds.splice(index, 1);
  }

  // Add the target workspace to parentWorkspaceIds if not already there
  if (!updatedElement.parentWorkspaceIds.includes(targetWorkspaceId)) {
    updatedElement.parentWorkspaceIds.push(targetWorkspaceId);
  }

  // Set workspaceParameters for new workspace
  updatedElement.workspaceParameters[targetWorkspaceId] = deepClone(
    updatedElement.workspaceParameters[currentWorkspaceId],
  );

  // Clean up the workspaceParameters for the old workspace
  delete updatedElement.workspaceParameters[currentWorkspaceId];

  // Update the element in the store
  await store.dispatch("updateSimpleElement", {
    simpleElement: updatedElement,
    undoable: true,
  });
};

export function getWorkspaceIdsAtFrame(sequence, frameIndex) {
  const roundIndex = Math.floor(frameIndex);
  const workspaceIds = new Set();
  // ...other actions start at the next frame
  sequence.frames[roundIndex + 1]?.forEach((action) => {
    if (action.importData?.sequenceId) {
      const sequence = store.state.sequences.get(action.importData?.sequenceId);
      workspaceIds.add(sequence.parentWorkspaceId);
    } else if (
      action.importData?.workspaceId &&
      action.metadata.type !== IMPORTED_SEQUENCE
    ) {
      workspaceIds.add(action.importData?.workspaceId);
    }
  });
  if (workspaceIds.size === 0) {
    workspaceIds.add(sequence.parentWorkspaceId);
  }
  return workspaceIds;
}

export const elementIdsAvailableAtFrame = (sequenceId, frameIndex) => {
  const sequence = store.state.sequences.get(sequenceId);
  // We list the workspaces that need to be visible for that frame based on the actions
  const workspaceIds = getWorkspaceIdsAtFrame(sequence, frameIndex);

  // Get expanded workspaces
  // Create a set to track all elements that should be visible
  const visibleElementIds = new Set();

  // Process all elements
  Array.from(store.state.allSimpleElements.values()).forEach((element) => {
    // Check if element belongs to a visible workspace
    const isInVisibleWorkspace = element.parentWorkspaceIds.some(
      (workspaceId) => workspaceIds.has(workspaceId),
    );

    if (isInVisibleWorkspace) {
      // Check if this element has an expanded workspace
      if (element.childWorkspaceId && isWorkspaceExpanded(element.id)) {
        // If expanded, don't show the element itself
        // Instead, show all elements from its child workspace
        const childWorkspaceId = element.childWorkspaceId;
        Array.from(store.state.allSimpleElements.values())
          .filter((childElement) =>
            childElement.parentWorkspaceIds.includes(childWorkspaceId),
          )
          .forEach((childElement) => {
            visibleElementIds.add(childElement.id);
          });
      } else {
        // Regular element or unexpanded workspace element
        visibleElementIds.add(element.id);
      }
    }
  });

  return Array.from(visibleElementIds);
};

const getElementsFromExpandedWorkspaces = () => {
  const elementIds = new Set();

  // Find all expanded workspaces and get their child elements
  for (const element of store.state.allSimpleElements.values()) {
    if (isWorkspaceExpanded(element.id) && element.childWorkspaceId) {
      // Get all elements that belong to this expanded workspace
      Array.from(store.state.allSimpleElements.values()).forEach(
        (childElement) => {
          if (
            childElement.parentWorkspaceIds.includes(element.childWorkspaceId)
          ) {
            elementIds.add(childElement.id);
          }
        },
      );
    }
  }

  return Array.from(elementIds);
};

export const elementIdsAvailable = () => {
  if (store.state.diagramMode === diagramMode.creationMode) {
    const elementIds = new Set();
    store.getters.currentWorkspaceSelectedElementsIds.forEach((elementId) =>
      elementIds.add(elementId),
    );
    getElementsFromExpandedWorkspaces().forEach((elementId) =>
      elementIds.add(elementId),
    );
    return Array.from(elementIds);
  }
  return elementIdsAvailableAtFrame(
    store.state.currentSequenceId,
    store.state.sequences.get(store.state.currentSequenceId).currentFrame,
  );
};

export const segmentIdsAvailableAtFrame = (sequenceId, frameIndex) => {
  const sequence = store.state.sequences.get(sequenceId);
  const workspaceIds = getWorkspaceIdsAtFrame(sequence, frameIndex);
  return Array.from(store.state.allSimpleSegments.values())
    .filter((segment) => {
      return segment.parentWorkspaceId.some((workspaceId) =>
        workspaceIds.has(workspaceId),
      );
    })
    .map((segment) => segment.id);
};

export const segmentIdsAvailable = (frameIndex) => {
  if (store.state.diagramMode === diagramMode.creationMode) {
    return Array.from(store.state.allSimpleSegments.keys());
  }
  return segmentIdsAvailableAtFrame(
    store.state.currentSequenceId,
    frameIndex ||
      store.state.sequences.get(store.state.currentSequenceId).currentFrame,
  );
};

export const getEffectiveWorkspaceIds = (elementId, frameIndex) => {
  const element = store.state.allSimpleElements.get(elementId);
  const currentWorkspaceId = store.state.currentWorkspaceId;

  // If we are in creation mode, return the current workspace ID
  if (store.state.diagramMode === diagramMode.creationMode) {
    const expandedWorkspaceIds = [];

    // Find all expanded workspaces
    for (const element of store.state.allSimpleElements.values()) {
      if (isWorkspaceExpanded(element.id) && element.childWorkspaceId) {
        expandedWorkspaceIds.push(element.childWorkspaceId);
      }
    }

    return [currentWorkspaceId, ...expandedWorkspaceIds];
  }

  // Otherwise we need to check the current sequence and current frame
  const sequence = store.state.sequences.get(store.state.currentSequenceId);
  if (!sequence) return [currentWorkspaceId];

  frameIndex = frameIndex || sequence.currentFrame;
  const frame = sequence.frames[Math.floor(frameIndex)];
  if (!frame) return [currentWorkspaceId];

  // Find the first workspace ID for the current frame that matches the element's parentWorkspaceIds
  const workspaceId = Array.from(
    getWorkspaceIdsAtFrame(sequence, frameIndex),
  ).find((workspaceId) => element.parentWorkspaceIds.includes(workspaceId));

  if (!workspaceId) return [store.state.currentWorkspaceId];

  return [workspaceId];
};
