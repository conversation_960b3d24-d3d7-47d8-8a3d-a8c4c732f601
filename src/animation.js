import { store } from "./store";
import {
  createElementFromState, deleteAllExpandedWorkspaces,
  diagramMode,
  elementIdsAvailable,
  elementIdsAvailableAtFrame,
  findConnectedAnchors,
  getEffectiveWorkspaceIds,
  getViewboxSurroundingAllElementsForWorkspace,
  getViewCenterCoordinates,
  getWorkspaceIdsAtFrame,
  hideImmersiveViewbox,
  hideItemPositions,
  hideVisibilityToggles,
  isEmptyObject,
  isWorkspaceExpanded,
  retraceAllSegments,
  setNormalBackground,
  setRecordingBackground,
  showItemPositions,
  sleep
} from "@/core";
import {
  ELEMENT_OPACTITY,
  ELEMENT_POSITION,
  IMPORTED_SEQUENCE,
  SEND_DATA,
  VIEWBOX,
  WORKSPACE_ZOOM_IN_1,
  WORKSPACE_ZOOM_IN_2,
  WORKSPACE_ZOOM_OUT_1,
  WORKSPACE_ZOOM_OUT_2,
} from "@/sequence";
import { draw } from "@/plugins/svgjs";
import { Timeline } from "@svgdotjs/svg.js";
import { drawAdapter } from "@/draw-adapter";
import { resetViewBounds } from "@/listeners";

let animationPauseIds = [];
let actionsFinished;
export const movingObjects = [];
export const staticObjects = [];
const HIDDEN_OPACITY_IN_RECORDING = 0.3;
const TIME_TO_CHANGE_OPACITY = 0.02;

/**
 * Gets the effective actions for a frame, filtering out marker actions
 * @param {string} sequenceId - The ID of the current sequence
 * @param {number} frameIndex - The index of the frame
 * @returns {Array} Filtered array of actions
 */
const getEffectiveActionsForFrame = (sequenceId, frameIndex) => {
  const sequence = store.state.sequences.get(sequenceId);
  if (!sequence || !sequence.frames[frameIndex]) return [];

  const frame = sequence.frames[frameIndex];

  // Filter out marker actions
  const nonMarkerActions = frame.filter(
    (action) => action.metadata.type !== IMPORTED_SEQUENCE,
  );

  // Check if this frame contains any actions from an imported sequence
  const hasImportedActions = nonMarkerActions.some(
    (action) => action.importData,
  );

  if (hasImportedActions) {
    // If we have imported actions, only return those (imported sequence has priority)
    // Get the importGroupId to ensure we only get actions from the same imported sequence
    const importGroupId = nonMarkerActions.find((action) => action.importData)
      ?.importData?.importGroupId;

    return nonMarkerActions.filter(
      (action) =>
        action.importData && action.importData.importGroupId === importGroupId,
    );
  } else {
    // If no imported actions, return all non-marker actions from the current sequence
    return nonMarkerActions;
  }
};

export const playSequence = async (sequenceId) => {
  async function playSendData() {
    for (
      let frameIndex = sequence.currentFrame;
      frameIndex < frames.length - 1;
      frameIndex++
    ) {
      if (!store.state.sequences.get(sequenceId).isPlaying) {
        return;
      }
      const frame = frames[frameIndex + 1];
      for (const action of frame) {
        if (action.metadata.type === SEND_DATA) {
          const workspaceIds = getEffectiveWorkspaceIds(
            action.data.elementSource,
            frameIndex + 1,
          );
          workspaceIds.forEach((workspaceId) => {
            displaySendData(
              action.data.elementSource,
              action.data.elementDestination,
              action.data.color,
              action.metadata.duration || 1,
              null,
              workspaceId,
            );
          });
        }
      }
      let pause = Math.max(
        ...frame.map((action) => action.metadata.duration || 1),
      );
      pause = isNaN(pause) ? 1 : pause;
      await new Promise((r) => {
        const animationPauseId = setTimeout(r, getRealTime(pause));
        animationPauseIds.push(animationPauseId);
      });
    }
  }

  let sequence = store.state.sequences.get(sequenceId);
  // No need to start playing if there is not a second frame to play to
  if (sequence.frames.length <= 1) {
    return;
  }
  // If we were at the last
  if (
    sequence.currentFrame === sequence.frames.length - 1 &&
    sequence.currentFrame !== 0
  ) {
    sequence.currentFrame = 0;
    setupObjectsAtFrame(sequenceId, 0);
    store
      .dispatch("updateSequenceAction", {
        id: sequence.id,
        sequence: sequence,
        undoable: false,
      })
      .then();
    await playSequence(sequence.id);
    return;
  }

  if (!sequence.isPaused) {
    forceStopAnimation(); // just to make sure everything is clean before starting
  }
  hideVisibilityToggles();
  movingObjects.forEach((object) => object.timeline().play());
  drawAdapter.parameters.allElements.forEach((element) =>
    element.graphicElement.timeline().play(),
  );
  let recording = store.state.diagramMode === diagramMode.recordingMode;
  drawAdapter.parameters.immersiveViewbox.opacity(0);
  if (recording) {
    setRecordingBackground();
  }
  sequence.isPlaying = true;
  const frames = sequence.frames;

  if (store.state.rifleMode) {
    store
      .dispatch("updateSequenceAction", {
        id: sequence.id,
        sequence: sequence,
        undoable: false,
      })
      .then();
    while (store.state.sequences.get(sequenceId).isPlaying) {
      playSendData().then();
      await sleep(200);
    }
  } else {
    let hidden = recording ? HIDDEN_OPACITY_IN_RECORDING : 0;
    if (Math.floor(sequence.currentFrame) > frames.length - 1) {
      sequence.currentFrame = 0;
      setupObjectsAtFrame(sequenceId, sequence.currentFrame);
    }
    let frameTicksElapsed = Math.round((sequence.currentFrame % 1) * 10) / 10;

    // Main Loop around frames
    for (
      let frameIndex = Math.floor(sequence.currentFrame);
      frameIndex < frames.length;
      frameIndex++
    ) {
      sequence = store.state.sequences.get(sequenceId);
      const frame = frames[frameIndex + 1]; // We constantly read next frame
      if (!frame) {
        sequence.currentFrame = frameIndex; // We set it to next frame just to finish on a round number before coming back to 0
        continue;
      }
      redrawElementsAtFrame(sequenceId, frameIndex);
      // Get effective actions for this frame (including imported sequences)
      const effectiveActions = getEffectiveActionsForFrame(
        sequenceId,
        frameIndex + 1,
      );
      let intervalTime = Math.max(
        ...effectiveActions.map((action) => action.metadata.duration || 1),
      );
      intervalTime = isNaN(intervalTime) ? 1 : intervalTime;
      if (sequence.isPaused && sequence.isPlaying) {
        frameTicksElapsed = 0;
        movingObjects.forEach((object) => object.timeline().play());
        drawAdapter.parameters.allElements.forEach((element) =>
          element.graphicElement.timeline().play(),
        );
        draw.timeline().play();
        sequence.isPaused = false;
        const frameTicksRemaining = 1 - (sequence.currentFrame % 1);
        const iterations =
          frameTicksRemaining === 1
            ? 10
            : Math.round((frameTicksRemaining % 1) * 10);
        for (let i = 0; i < iterations; i++) {
          if (sequence.isPaused) {
            break;
          }
          sequence.lastIteration = Date.now();
          if (i === 0) {
            const remainingTimeInFirstIteration =
              getRealTime(intervalTime) / 10 - sequence.elapsedBeforePause;
            await new Promise((r) => {
              const animationPauseId = setTimeout(
                r,
                remainingTimeInFirstIteration,
              );
              animationPauseIds.push(animationPauseId);
            });
          } else {
            await new Promise((r) => {
              const animationPauseId = setTimeout(
                r,
                getRealTime(intervalTime) / 10,
              );
              animationPauseIds.push(animationPauseId);
            });
          }
          sequence.elapsedBeforePause = 0;
          sequence.currentFrame =
            Math.round((sequence.currentFrame + 1 / 10) * 10) / 10;
          store
            .dispatch("updateSequenceAction", {
              id: sequence.id,
              sequence: sequence,
              undoable: false,
            })
            .then();
        }
        if (!sequence.isPaused) {
          forceStopAnimation();
        }
        continue;
      }
      if (!sequence.isPlaying && sequence.isPaused) {
        break;
      }

      // Count the number of actions (excluding VIEWBOX if not in immersive view)
      const numberOfActions = effectiveActions.filter((action) =>
        store.state.immersiveView ? true : action.metadata.type !== VIEWBOX,
      ).length;
      actionsFinished = 0;

      // Loop around actions
      for (const action of effectiveActions) {
        const actionDuration = action.metadata.duration || 1;
        if (action.metadata.type === SEND_DATA) {
          const workspaceIds = getEffectiveWorkspaceIds(
            action.data.elementSource,
            frameIndex,
          );
          workspaceIds.forEach((workspaceId) => {
            displaySendData(
              action.data.elementSource,
              action.data.elementDestination,
              action.data.color,
              actionDuration,
              frameTicksElapsed,
              workspaceId,
            );
          });
        } else if (
          action.metadata.type === VIEWBOX &&
          store.state.immersiveView
        ) {
          const viewbox = getResizedViewbox(
            action.data.position.x,
            action.data.position.y,
            action.data.position.width,
            action.data.position.height,
            sequence.dimensions?.windowWidth,
            sequence.dimensions?.windowHeight,
          );
          draw
            .animate({
              duration: getRealTime(actionDuration),
              when: "now",
            })
            .ease("-")
            .viewbox(viewbox.x, viewbox.y, viewbox.width, viewbox.height)
            .after(() => {
              actionsFinished++;
            });
        } else if (action.metadata.type === ELEMENT_POSITION) {
          const element = drawAdapter.parameters.allElements.get(
            action.data.elementId,
          );
          const simpleElement = store.state.allSimpleElements.get(
            action.data.elementId,
          );
          const entityOffsetX =
            element.graphicElement.cx() - element.graphicElement.entity.cx();
          const entityOffsetY =
            element.graphicElement.cy() - element.graphicElement.entity.cy();
          if (
            simpleElement.workspaceParameters[
              getEffectiveWorkspaceIds(element.id, frameIndex)[0]
            ].destinationElementIds.length === 0
          ) {
            element.graphicElement
              .animate({
                duration: getRealTime(actionDuration),
                when: "now",
              })
              .ease("-")
              .center(
                action.data.position.x + entityOffsetX,
                action.data.position.y + entityOffsetY,
              )
              .after(() => actionsFinished++);
          }
        } else if (action.metadata.type === ELEMENT_OPACTITY) {
          let element = drawAdapter.parameters.allElements.get(
            action.data.elementId,
          );
          if (action.data.opacity === 1) {
            element.graphicElement
              .animate({
                delay:
                  getRealTime(actionDuration) -
                  getRealTime(actionDuration) * TIME_TO_CHANGE_OPACITY,
                duration: getRealTime(actionDuration) * TIME_TO_CHANGE_OPACITY,
                when: "now",
              })
              .opacity(1)
              .after(() => actionsFinished++);
          } else if (action.data.opacity === 0) {
            element.graphicElement
              .animate({
                delay:
                  getRealTime(actionDuration) -
                  getRealTime(actionDuration) * TIME_TO_CHANGE_OPACITY,
                duration: getRealTime(actionDuration) * TIME_TO_CHANGE_OPACITY,
                when: "now",
              })
              .opacity(hidden)
              .after(() => actionsFinished++);
          }
        } else if (action.metadata.type === WORKSPACE_ZOOM_IN_1) {
          // Get the element containing the workspace
          const element = store.state.allSimpleElements.get(
            action.data.elementId,
          );
          if (element) {
            // First we position the view at the center of the element but not zoomed in
            draw
              .animate({
                duration: getRealTime(actionDuration * 0.1),
                when: "now",
              })
              .ease("<")
              .zoom(1, {
                x: element.workspaceParameters[store.state.currentWorkspaceId]
                  .x,
                y: element.workspaceParameters[store.state.currentWorkspaceId]
                  .y,
              })
              .after(() => {
                // And then zoom into the element
                draw
                  .animate({
                    duration: getRealTime(actionDuration * 0.9),
                    when: "now",
                  })
                  .ease("<")
                  .zoom(20, {
                    x: element.workspaceParameters[
                      store.state.currentWorkspaceId
                    ].x,
                    y: element.workspaceParameters[
                      store.state.currentWorkspaceId
                    ].y,
                  })
                  .after(() => {
                    actionsFinished++;
                  });
              });
          }
        } else if (action.metadata.type === WORKSPACE_ZOOM_IN_2) {
          // Get the element containing the workspace
          const element = store.state.allSimpleElements.get(
            action.data.elementId,
          );
          if (element) {
            // First we position the view zoomed out
            draw.viewbox(0, 0, 50000, 50000);
            const viewboxCoordinates =
              getViewboxSurroundingAllElementsForWorkspace(
                store.state.currentWorkspaceId,
              );
            if (viewboxCoordinates === null) {
              draw.viewbox(2500, 2500, 10000, 10000);
            } else {
              draw
                .animate(getRealTime(actionDuration))
                .viewbox(
                  viewboxCoordinates.newViewboxX,
                  viewboxCoordinates.newViewboxY,
                  viewboxCoordinates.screenWidth,
                  viewboxCoordinates.screenHeight,
                )
                .after(() => {
                  actionsFinished++;
                });
            }
          }
        } else if (action.metadata.type === WORKSPACE_ZOOM_OUT_1) {
          // Animate view to zoom out
          const { centerX, centerY } = getViewCenterCoordinates();

          // Zoom out
          draw
            .animate({
              duration: getRealTime(actionDuration),
              when: "now",
            })
            .ease(">")
            .zoom(0.1, { x: centerX, y: centerY })
            .after(() => {
              actionsFinished++;
            });
        } else if (action.metadata.type === WORKSPACE_ZOOM_OUT_2) {
          // Position the view at the center of the element
          const element = store.state.allSimpleElements.get(
            action.data.elementId,
          );
          const workspaceId = store.state.currentWorkspaceId;
          draw.zoom(20, {
            x: element.workspaceParameters[workspaceId].x,
            y: element.workspaceParameters[workspaceId].y,
          });

          // Zoom out

          const viewboxCoordinates =
            getViewboxSurroundingAllElementsForWorkspace(workspaceId);
          if (viewboxCoordinates === null) {
            draw.viewbox(2500, 2500, 10000, 10000);
          } else {
            draw
              .animate(getRealTime(actionDuration))
              .viewbox(
                viewboxCoordinates.newViewboxX,
                viewboxCoordinates.newViewboxY,
                viewboxCoordinates.screenWidth,
                viewboxCoordinates.screenHeight,
              )
              .after(() => {
                actionsFinished++;
              });
          }
        }
      }

      animateSegmentsVisibility(sequenceId, frameIndex, true);

      sequence.elapsedBeforePause = 0;
      for (let i = frameTicksElapsed * 10; i < 10; i++) {
        // There is inconsistency between the time in this loop and actions finished, so we finish as soon as one of the two conditions is met
        // However, below 5 actions, we want to wait for the last one to finish regardless (that's a bit of a hack)
        if (
          sequence.isPaused ||
          (actionsFinished >= numberOfActions && numberOfActions > 5)
        ) {
          break;
        }
        sequence.lastIteration = Date.now();
        sequence.currentFrame = frameIndex + i / 10;
        store
          .dispatch("updateSequenceAction", {
            id: sequence.id,
            sequence: sequence,
            undoable: false,
          })
          .then();
        await new Promise((r) => {
          const animationPauseId = setTimeout(
            r,
            getRealTime(intervalTime) / 10,
          );
          animationPauseIds.push(animationPauseId);
        });
      }
      frameTicksElapsed = 0;
      sequence.elapsedBeforePause = 0;
      if (!sequence.isPaused) {
        forceStopAnimation();
      }
    }
    if (sequence.isPlaying) {
      if (
        sequence.currentFrame >= frames.length - 1 &&
        store.state.autoReturnToFirstFrame
      ) {
        sequence.currentFrame = 0;
        setupObjectsAtFrame(sequenceId, sequence.currentFrame);
      }
      sequence.isPlaying = false;
      if (store.state.repeat) {
        await playSequence(sequenceId);
      }
      store
        .dispatch("updateSequenceAction", {
          id: sequence.id,
          sequence: sequence,
          undoable: false,
        })
        .then();
    }
  }
};

export const forceStopAnimation = () => {
  movingObjects.forEach((object) => object.remove());
  staticObjects.forEach((object) => object.remove());
  movingObjects.length = 0;
  staticObjects.length = 0;
  animationPauseIds.forEach((animationPauseId) =>
    clearTimeout(animationPauseId),
  );
  animationPauseIds = [];
  draw.timeline().pause();
  draw.timeline(new Timeline());
  drawAdapter.parameters.allElements.forEach((element) => {
    element.graphicElement.timeline().pause();
    element.graphicElement.timeline(new Timeline());
  });
};

export function playFrame(
  sequenceId,
  frameIndex,
  reversed,
  offset,
  overrideDuration,
) {
  let sequence = store.state.sequences.get(sequenceId);
  if (sequence.isPaused) {
    sequence.isPaused = false;
  }

  // Get effective actions for this frame (including imported sequences)
  const effectiveActions = getEffectiveActionsForFrame(sequenceId, frameIndex);

  if (effectiveActions.length === 0) {
    return false;
  }
  let ranDisplaySendDataActions = false;
  for (const action of effectiveActions) {
    if (!reversed) {
      if (action.metadata.type === SEND_DATA) {
        displaySendData(
          action.data.elementSource,
          action.data.elementDestination,
          action.data.color,
          overrideDuration ? 1 : action.metadata.duration,
          offset,
        );
        ranDisplaySendDataActions = true;
      }
    } else {
      if (action.metadata.type === SEND_DATA) {
        displaySendData(
          action.data.elementDestination,
          action.data.elementSource,
          action.data.color,
          overrideDuration ? 1 : action.metadata.duration,
          1 - offset,
        );
        ranDisplaySendDataActions = true;
      }
    }
  }
  return ranDisplaySendDataActions;
}

export const pauseSequence = async (sequenceId) => {
  const pausedTime = Date.now();
  const sequence = store.state.sequences.get(sequenceId);
  if (sequence.currentFrame % 1 === 0) {
    // if we land on a rounded frame we stop the animation. Less weirdness that way
    sequence.isPlaying = false;
    sequence.isPaused = false;
    forceStopAnimation();
    store
      .dispatch("updateSequenceAction", {
        id: sequence.id,
        sequence: sequence,
        undoable: false,
      })
      .then();

    setupObjectsAtFrame(sequenceId, sequence.currentFrame);
    return;
  }
  sequence.elapsedBeforePause =
    sequence.elapsedBeforePause + pausedTime - sequence.lastIteration;
  sequence.isPlaying = false;
  sequence.isPaused = true;
  animationPauseIds.forEach((animationPauseId) =>
    clearTimeout(animationPauseId),
  );
  animationPauseIds = [];
  movingObjects.forEach((object) => object.timeline().pause());
  drawAdapter.parameters.allElements.forEach((element) =>
    element.graphicElement.timeline().pause(),
  );
  draw.timeline().pause();
  store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence: sequence,
    undoable: false,
  });
  elementIdsAvailable().forEach((element) => {
    setDisplayToggleFromCurrentVisibility(sequence, element.id);
  });
};

export const stopSequence = async (sequenceId) => {
  const sequence = store.state.sequences.get(sequenceId);
  if (!sequence) {
    return;
  }
  sequence.isPlaying = false;
  sequence.isPaused = false;
  sequence.currentFrame = 0;
  forceStopAnimation();
  store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence: sequence,
    undoable: false,
  });

  setupObjectsAtFrame(sequenceId, 0);
};

export const displaySendData = (
  sourceElementId,
  destinationElementId,
  color,
  duration = 1,
  offset,
  workspaceId,
) => {
  color = color || "#000";
  offset = offset || 0;
  const sourceElement = drawAdapter.parameters.allElements.get(sourceElementId);
  sourceElement.graphicElement.entity
    .fill("#fac988")
    .animate({ duration: getRealTime(duration) / 3, when: "now" })
    .fill(sourceElement.graphicElement.entity.color.substring(0, 7)) // this is because of a weird bug preventing from animating to transparent color
    .after(() => {
      sourceElement.graphicElement.entity.fill(
        sourceElement.graphicElement.entity.color,
      );
    });
  const connectedAnchors = findConnectedAnchors(
    sourceElementId,
    destinationElementId,
    workspaceId,
  );
  if (!isEmptyObject(connectedAnchors)) {
    let circle = draw
      .circle(10)
      .fill(color)
      .x(connectedAnchors.anchorFrom.x)
      .y(connectedAnchors.anchorFrom.y);
    circle.front();
    movingObjects.push(circle);
    const foundSimpleSegmentEntry = Array.from(
      store.state.allSimpleSegments,
    ).find(
      ([key, value]) =>
        (value.segmentElementsInfo.element1 === sourceElementId &&
          value.segmentElementsInfo.element2 === destinationElementId) ||
        (value.segmentElementsInfo.element1 === destinationElementId &&
          value.segmentElementsInfo.element2 === sourceElementId &&
          key !== null),
    );
    const foundSegmentId = foundSimpleSegmentEntry
      ? foundSimpleSegmentEntry[0]
      : null;
    const foundSegment = drawAdapter.parameters.allSegments.get(foundSegmentId);
    let foundSimpleSegmentEntryElement = foundSimpleSegmentEntry[1];
    const reverse =
      sourceElementId ===
      foundSimpleSegmentEntryElement.segmentElementsInfo.element2;
    circle
      .animate({ duration: getRealTime(duration), when: "now" })
      .progress(offset)
      .during(function (pos) {
        const p = foundSegment.graphicSegment.pointAt(
          pos * foundSegment.graphicSegment.length(),
        );
        circle.center(p.x, p.y);
      })
      .reverse(reverse)
      .after(() => {
        circle.remove();
        actionsFinished++;
      });
  }
};

export const elementIsVisibleAtFrame = (elementId, sequenceId, frameIndex) => {
  const sequence = store.state.sequences.get(sequenceId);
  if (sequence) {
    // Check if the element is part of an expanded workspace
    const element = store.state.allSimpleElements.get(elementId);
    if (element) {
      // Check if this element is from a workspace that's expanded
      for (const parentWorkspaceId of element.parentWorkspaceIds) {
        // Find the element that contains this workspace
        const workspaceElement = Array.from(
          store.state.allSimpleElements.values(),
        ).find((el) => el.childWorkspaceId === parentWorkspaceId);

        if (workspaceElement && isWorkspaceExpanded(workspaceElement.id)) {
          // If the workspace is expanded, check if the workspace element is visible
          return elementIsVisibleAtFrame(
            workspaceElement.id,
            sequenceId,
            frameIndex,
          );
        }
      }
    }

    // Normal visibility check
    for (let i = frameIndex; i >= 0; i--) {
      // Get effective actions for this frame (including imported sequences)
      const effectiveActions = getEffectiveActionsForFrame(sequenceId, i);

      // Look for opacity actions in the effective actions
      const action = effectiveActions.find(
        (action) =>
          action.data?.elementId === elementId &&
          action.metadata.type === ELEMENT_OPACTITY,
      );

      if (action) {
        return action.data.opacity === 1;
      }
    }
  }
  return true;
};

export const redrawElementsAtFrame = (sequenceId, frameIndex) => {
  deleteAllExpandedWorkspaces();;
  drawAdapter.deleteAllSegments();
  const displayShadows = store.state.displayShadows;
  const elementIds = elementIdsAvailableAtFrame(sequenceId, frameIndex);
  const sequence = store.state.sequences.get(sequenceId);
  const workspaceIds = getWorkspaceIdsAtFrame(sequence, frameIndex);

  // delete elements that shouldn't be there
  drawAdapter.parameters.allElements.forEach((element) => {
    if (!elementIds.includes(element.id)) {
      drawAdapter.deleteElement(element.id, true);
    }
  });

  for (const elementId of elementIds) {
    // if element already exists in drawAdapter.parameters.allElements we skip
    if (drawAdapter.parameters.allElements.has(elementId)) {
      continue;
    }
    // get the workspaceId for the element using workspaceIds and element parentWorkspaceId
    const workspaceId = Array.from(workspaceIds).find((workspaceId) => {
      return store.state.allSimpleElements
        .get(elementId)
        .parentWorkspaceIds.includes(workspaceId);
    });

    const simpleElement = createElementFromState(
      elementId,
      workspaceId,
      displayShadows,
    );
    // get element position at frame
    const position = sequence.frames[frameIndex]?.find(
      (action) =>
        action.data.elementId === simpleElement.id &&
        action.data.position !== null,
    )?.data.position;
    if (position) {
      drawAdapter.setPosition(simpleElement.id, position.x, position.y);
    }
  }
  elementIds.forEach((elementId) => {
    retraceAllSegments(elementId, frameIndex);
  });
};

const frameHasViewAction = (effectiveFrame) => {
  return effectiveFrame.some(
    (action) =>
      action.metadata.type === VIEWBOX ||
      action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
      action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
      action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
      action.metadata.type === WORKSPACE_ZOOM_OUT_2,
  );
};

export const setupObjectsAtFrame = (sequenceId, frameIndex, animate) => {
  const sequence = store.state.sequences.get(sequenceId);
  const frameTicksElapsed = frameIndex % 1;

  if (!sequence) return;

  redrawElementsAtFrame(sequenceId, frameIndex);

  const recording = store.state.diagramMode === diagramMode.recordingMode;
  const hidden = recording ? HIDDEN_OPACITY_IN_RECORDING : 0;

  // Set background color
  setRecordingOrNormalBackground(recording, frameIndex, sequence);

  // Process the frame and any imported sequences
  const roundedFrameIndex = Math.floor(frameIndex);
  const effectiveFrame = getEffectiveActionsForFrame(
    sequenceId,
    roundedFrameIndex,
  );

  // Check for viewbox or zoom actions in the effective frame
  if (frameHasViewAction(effectiveFrame)) {
    // Use the updated getInterpolatedViewbox that handles zoom actions
    const viewbox = getInterpolatedViewbox(
      sequence,
      roundedFrameIndex,
      frameTicksElapsed,
    );

    if (viewbox) {
      applyViewbox(viewbox, sequence.dimensions, animate);
      if (recording) applyRecordingViewbox(viewbox);
    } else {
      hideImmersiveViewbox();
    }
  } else {
    hideImmersiveViewbox();
  }

  // Set moving objects (black dot) - need to consider imported sequences
  setupMovingObjects(sequence, frameIndex, frameTicksElapsed);

  // Set positions and visibility for elements - need to consider imported sequences
  setupElementPositionsAndVisibility(
    sequence,
    frameIndex,
    frameTicksElapsed,
    hidden,
    animate,
    effectiveFrame, // Pass the effective frame with imported sequence actions
  );

  animateSegmentsVisibility(sequenceId, frameIndex, false);
};

export const setupElementsForCreation = () => {
  drawAdapter.parameters.allElements.forEach((element) => {
    element.graphicElement.entity.opacity(1);
    const simpleElement = store.state.allSimpleElements.get(element.id);
    if (
      !simpleElement.parentWorkspaceIds.includes(store.state.currentWorkspaceId)
    ) {
      return;
    }
    if (
      simpleElement.workspaceParameters[store.state.currentWorkspaceId]
        .destinationElementIds.length > 0
    ) {
      return;
    }
    const entityOffsetX =
      element.graphicElement.cx() - element.graphicElement.entity.cx();
    const entityOffsetY =
      element.graphicElement.cy() - element.graphicElement.entity.cy();

    // Get position from workspaceParameters if available for the current workspace
    let x = simpleElement.workspaceParameters[store.state.currentWorkspaceId].x;
    let y = simpleElement.workspaceParameters[store.state.currentWorkspaceId].y;
    element.graphicElement.center(x + entityOffsetX, y + entityOffsetY);
  });
};

const setRecordingOrNormalBackground = (recording, frame, sequence) => {
  if (recording) {
    setRecordingBackground();
    if (shouldSetImmersiveedViewboxBackground(frame, sequence)) {
      drawAdapter.parameters.immersiveViewbox.fill("#f1ffea");
    } else {
      drawAdapter.parameters.immersiveViewbox.fill("#ffffff");
    }
  } else {
    drawAdapter.parameters.immersiveViewbox.fill("#ffffff");
    setNormalBackground();
  }
};

const shouldSetImmersiveedViewboxBackground = (frame, sequence) => {
  // Get effective actions for this frame, respecting imported sequence priority
  const effectiveActions = getEffectiveActionsForFrame(sequence.id, frame);

  // Check if any of the effective actions are non-interpolated viewbox actions
  return effectiveActions.some(
    (action) =>
      action.metadata.type === VIEWBOX && !action.metadata.interpolated,
  );
};

const getInterpolatedViewbox = (sequence, frame, frameTicksElapsed) => {
  let currentView = {
    x: draw.viewbox().x,
    y: draw.viewbox().y,
    width: draw.viewbox().width,
    height: draw.viewbox().height,
  };

  // Get effective actions for the current frame to determine if we're in an imported sequence
  const effectiveActions = getEffectiveActionsForFrame(sequence.id, frame);
  const isImportedSequenceFrame = effectiveActions.some(
    (action) => action.importData,
  );

  // Check if there are any viewbox or zoom actions in the current frame
  if (!frameHasViewAction(sequence.frames[frame])) {
    return currentView;
  }

  for (let i = frame; i >= 0; i--) {
    // Get effective actions for each previous frame, respecting the same imported/current sequence logic
    const frameEffectiveActions = getEffectiveActionsForFrame(sequence.id, i);

    if (frameHasViewAction(frameEffectiveActions)) {
      return interpolateViewbox(
        sequence,
        i,
        frameTicksElapsed,
        isImportedSequenceFrame,
      );
    }
  }
  return currentView;
};

const interpolateViewbox = (sequence, frameIndex, frameTicksElapsed) => {
  const nextFrame = sequence.frames[frameIndex + 1];

  // Get the effective actions for the current and next frames, respecting imported sequence priority
  const effectiveCurrentFrameActions = getEffectiveActionsForFrame(
    sequence.id,
    frameIndex,
  );
  const effectiveNextFrameActions = nextFrame
    ? getEffectiveActionsForFrame(sequence.id, frameIndex + 1)
    : [];

  // Find the viewbox for start of frame. Could be viewbox or zoom action
  const currentViewboxAction =
    effectiveCurrentFrameActions.find(
      (action) => action.metadata.type === VIEWBOX,
    ) ||
    effectiveCurrentFrameActions.find(
      (action) =>
        action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
        action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
        action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
        action.metadata.type === WORKSPACE_ZOOM_OUT_2,
    );

  const nextViewboxAction =
    effectiveNextFrameActions.find(
      (action) => action.metadata.type === VIEWBOX,
    ) ||
    effectiveNextFrameActions.find(
      (action) =>
        action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
        action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
        action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
        action.metadata.type === WORKSPACE_ZOOM_OUT_2,
    );

  // if (currentViewboxAction && nextViewboxAction) {
  // Both frames have viewbox actions, interpolate between them
  let currentViewbox;
  let nextViewbox;

  // canvas dimensions. Divide by 20 because 10000 / 500 = 20, 10000 being reference and 500 being zoomed desired width
  const canvasWidth = window.innerWidth / 20;
  const canvasHeight = window.innerHeight / 20;

  if (currentViewboxAction.metadata.type === WORKSPACE_ZOOM_IN_1) {
    // Means we start zoomed out of imported workspace and finished zoomed into the workspace
    currentViewbox = { x: 0, y: 0, width: 50000, height: 50000 };
    const viewboxCoordinatesSurrounding =
      getViewboxSurroundingAllElementsForWorkspace(
        nextViewboxAction.importData.workspaceId,
      );
    nextViewbox = {
      x: viewboxCoordinatesSurrounding.newViewboxX,
      y: viewboxCoordinatesSurrounding.newViewboxY,
      width: viewboxCoordinatesSurrounding.screenWidth,
      height: viewboxCoordinatesSurrounding.screenHeight,
    };
  } else if (currentViewboxAction.metadata.type === WORKSPACE_ZOOM_IN_2) {
    // Means we start zoomed into the workspace
    const viewboxCoordinatesSurrounding =
      getViewboxSurroundingAllElementsForWorkspace(
        currentViewboxAction.importData.workspaceId,
      );
    currentViewbox = {
      x: viewboxCoordinatesSurrounding.newViewboxX,
      y: viewboxCoordinatesSurrounding.newViewboxY,
      width: viewboxCoordinatesSurrounding.screenWidth,
      height: viewboxCoordinatesSurrounding.screenHeight,
    };
  } else if (currentViewboxAction.metadata.type === WORKSPACE_ZOOM_OUT_1) {
    // Means we start zoomed into the element containing imported workspace and zoom out into workspace
    const parentElement = store.state.allSimpleElements.get(
      currentViewboxAction.data.elementId,
    );

    currentViewbox = {
      x:
        parentElement.workspaceParameters[store.state.currentWorkspaceId].x -
        canvasWidth / 2,
      y:
        parentElement.workspaceParameters[store.state.currentWorkspaceId].y -
        canvasHeight / 2,
      width: 500,
      height: 500,
    };

    const viewboxCoordinatesSurrounding =
      getViewboxSurroundingAllElementsForWorkspace(
        store.state.currentWorkspaceId,
      );
    nextViewbox = {
      x: viewboxCoordinatesSurrounding.newViewboxX,
      y: viewboxCoordinatesSurrounding.newViewboxY,
      width: viewboxCoordinatesSurrounding.screenWidth,
      height: viewboxCoordinatesSurrounding.screenHeight,
    };
  } else if (currentViewboxAction.metadata.type === WORKSPACE_ZOOM_OUT_2) {
    // Means we start zoomed into the workspace
    const viewboxCoordinatesSurrounding =
      getViewboxSurroundingAllElementsForWorkspace(
        store.state.currentWorkspaceId,
      );
    currentViewbox = {
      x: viewboxCoordinatesSurrounding.newViewboxX,
      y: viewboxCoordinatesSurrounding.newViewboxY,
      width: viewboxCoordinatesSurrounding.screenWidth,
      height: viewboxCoordinatesSurrounding.screenHeight,
    };
  } else if (currentViewboxAction.metadata.type === VIEWBOX) {
    currentViewbox = currentViewboxAction.data.position;
  }

  if (nextViewboxAction) {
    if (nextViewboxAction.metadata.type === WORKSPACE_ZOOM_IN_1) {
      // Means we end up zoomed into the element containing the imported workspace
      const parentElement = store.state.allSimpleElements.get(
        nextViewboxAction.data.elementId,
      );

      nextViewbox = {
        x:
          parentElement.workspaceParameters[store.state.currentWorkspaceId].x -
          canvasWidth / 2,
        y:
          parentElement.workspaceParameters[store.state.currentWorkspaceId].y -
          canvasHeight / 2,
        width: 500,
        height: 500,
      };
    } else if (nextViewboxAction.metadata.type === WORKSPACE_ZOOM_IN_2) {
      // Means we end up zoomed into imported workspace
      const viewboxCoordinatesSurrounding =
        getViewboxSurroundingAllElementsForWorkspace(
          nextViewboxAction.importData.workspaceId,
        );
      nextViewbox = {
        x: viewboxCoordinatesSurrounding.newViewboxX,
        y: viewboxCoordinatesSurrounding.newViewboxY,
        width: viewboxCoordinatesSurrounding.screenWidth,
        height: viewboxCoordinatesSurrounding.screenHeight,
      };
    } else if (nextViewboxAction.metadata.type === WORKSPACE_ZOOM_OUT_1) {
      // Means we end up zoomed out of imported workspace
      nextViewbox = { x: 0, y: 0, width: 50000, height: 50000 };
    } else if (nextViewboxAction.metadata.type === WORKSPACE_ZOOM_OUT_2) {
      // Means we end up zoomed into current workspace
      const viewboxCoordinatesSurrounding =
        getViewboxSurroundingAllElementsForWorkspace(
          store.state.currentWorkspaceId,
        );
      nextViewbox = {
        x: viewboxCoordinatesSurrounding.newViewboxX,
        y: viewboxCoordinatesSurrounding.newViewboxY,
        width: viewboxCoordinatesSurrounding.screenWidth,
        height: viewboxCoordinatesSurrounding.screenHeight,
      };
    } else if (nextViewboxAction.metadata.type === VIEWBOX) {
      nextViewbox = nextViewboxAction.data.position;
    }
  }
  if (currentViewbox && nextViewbox) {
    return {
      x:
        currentViewbox.x * (1 - frameTicksElapsed) +
        nextViewbox.x * frameTicksElapsed,
      y:
        currentViewbox.y * (1 - frameTicksElapsed) +
        nextViewbox.y * frameTicksElapsed,
      width:
        currentViewbox.width * (1 - frameTicksElapsed) +
        nextViewbox.width * frameTicksElapsed,
      height:
        currentViewbox.height * (1 - frameTicksElapsed) +
        nextViewbox.height * frameTicksElapsed,
    };
  }

  // Only current frame has a viewbox action
  else if (currentViewbox) {
    return {
      x: currentViewbox.x,
      y: currentViewbox.y,
      width: currentViewbox.width,
      height: currentViewbox.height,
    };
  } else {
    // No viewbox actions found in effective actions
    return {
      x: draw.viewbox().x,
      y: draw.viewbox().y,
      width: draw.viewbox().width,
      height: draw.viewbox().height,
    };
  }
};

const applyViewbox = (viewbox, dimensions, animate) => {
  if (store.state.immersiveView) {
    const resizedViewbox = getResizedViewbox(
      viewbox.x,
      viewbox.y,
      viewbox.width,
      viewbox.height,
      dimensions.windowWidth,
      dimensions.windowHeight,
    );
    if (animate) {
      draw
        .animate({ duration: 200, when: "now" })
        .viewbox(
          +resizedViewbox.x,
          +resizedViewbox.y,
          +resizedViewbox.width,
          +resizedViewbox.height,
        );
    } else {
      draw.viewbox(
        +resizedViewbox.x,
        +resizedViewbox.y,
        +resizedViewbox.width,
        +resizedViewbox.height,
      );
    }
  }
};

const applyRecordingViewbox = (viewbox) => {
  drawAdapter.parameters.immersiveViewbox
    .width((window.innerWidth * viewbox.width) / 10000)
    .height((window.innerHeight * viewbox.height) / 10000)
    .x(viewbox.x)
    .y(viewbox.y)
    .opacity(0.4)
    .stroke({ color: "#ff0000", width: 2 });
};

const setupMovingObjects = (sequence, frame, frameTicksElapsed) => {
  const nextFrameIndex = Math.floor(frame + 1); // +1 because sendData is defined on next frame

  // Get effective actions for the next frame (including imported sequences)
  const effectiveActions = getEffectiveActionsForFrame(
    sequence.id,
    nextFrameIndex,
  );
  if (effectiveActions.length === 0) return;

  effectiveActions.forEach((action) => {
    if (action.metadata.type === SEND_DATA && frameTicksElapsed !== 0) {
      const sourceElementId = action.data.elementSource;
      const destinationElementId = action.data.elementDestination;

      if (sourceElementId && destinationElementId) {
        const color = action.data.color || "#000";
        const workspaceIds = getEffectiveWorkspaceIds(sourceElementId);
        workspaceIds.forEach((workspaceId) => {
          const connectedAnchors = findConnectedAnchors(
            sourceElementId,
            destinationElementId,
            workspaceId,
          );
          if (!isEmptyObject(connectedAnchors)) {
            const foundSimpleSegment = findSimpleSegment(
              sourceElementId,
              destinationElementId,
            );
            const foundSegment = drawAdapter.parameters.allSegments.get(
              foundSimpleSegment.id,
            );
            const reverse =
              sourceElementId ===
              foundSimpleSegment.segmentElementsInfo.element2;
            const circlePoint = foundSegment.graphicSegment.pointAt(
              foundSegment.graphicSegment.length() *
                (reverse ? 1 - frameTicksElapsed : frameTicksElapsed),
            );
            const circle = draw
              .circle(10)
              .fill(color)
              .center(circlePoint.x, circlePoint.y);
            staticObjects.push(circle);
          }
        });
      }
    }
  });
};

const findSimpleSegment = (sourceElementId, destinationElementId) => {
  const foundSimpleSegmentEntry = Array.from(
    store.state.allSimpleSegments,
  ).find(
    ([, value]) =>
      (value.segmentElementsInfo.element1 === sourceElementId &&
        value.segmentElementsInfo.element2 === destinationElementId) ||
      (value.segmentElementsInfo.element1 === destinationElementId &&
        value.segmentElementsInfo.element2 === sourceElementId),
  );
  return foundSimpleSegmentEntry ? foundSimpleSegmentEntry[1] : null;
};

const setupElementPositionsAndVisibility = (
  sequence,
  frameIndex,
  frameTicksElapsed,
  hidden,
  animate,
  effectiveFrame,
) => {
  const roundedFrame = Math.floor(frameIndex);

  drawAdapter.parameters.allElements.forEach((element) => {
    const simpleElement = store.state.allSimpleElements.get(element.id);

    if (
      !simpleElement.parentWorkspaceIds.includes(store.state.currentWorkspaceId)
    ) {
      return;
    }

    if (
      simpleElement.workspaceParameters[store.state.currentWorkspaceId]
        .destinationElementIds.length > 0
    ) {
      return;
    }

    const action = findElementLatestAction(
      sequence,
      element.id,
      roundedFrame,
      effectiveFrame,
    );
    if (action) {
      const { x, y } = interpolateElementPosition(
        action.data.position,
        findElementLatestAction(sequence, element.id, roundedFrame + 1, null)
          ?.data.position || action.data.position,
        frameTicksElapsed,
      );
      const entityOffsetX =
        element.graphicElement.cx() - element.graphicElement.entity.cx();
      const entityOffsetY =
        element.graphicElement.cy() - element.graphicElement.entity.cy();

      if (animate) {
        element.graphicElement
          .animate({ duration: 300, when: "now" })
          .center(x + entityOffsetX, y + entityOffsetY);
      } else {
        element.graphicElement.center(x + entityOffsetX, y + entityOffsetY);
      }
    }

    setElementOpacity(
      element,
      sequence,
      roundedFrame,
      hidden,
      frameTicksElapsed,
      animate,
    );
    if (store.state.diagramMode === diagramMode.recordingMode) {
      setDisplayToggleFromCurrentVisibility(sequence, element.id);
    }
  });
  elementIdsAvailable().forEach((id) => {
    hideItemPositions(id);
    showItemPositions(id);
  });
};

const findElementLatestAction = (
  sequence,
  elementId,
  frameIndex,
  effectiveFrame,
) => {
  // If effectiveFrame is provided, check it first for the action
  if (effectiveFrame) {
    const action = effectiveFrame.find(
      (action) =>
        action.metadata.type === ELEMENT_POSITION &&
        action.data.elementId === elementId,
    );
    if (action) return action;
  }

  // Fall back to the original implementation
  for (let i = frameIndex; i >= 0; i--) {
    // Get effective actions for this frame (including imported sequences)
    const frameActions = getEffectiveActionsForFrame(sequence.id, i);

    // Look for the action in the effective actions
    const action = frameActions.find(
      (action) =>
        action.metadata.type === ELEMENT_POSITION &&
        action.data.elementId === elementId,
    );

    if (action) return action;
  }
};

const interpolateElementPosition = (
  currentPosition,
  nextPosition,
  frameTicksElapsed,
) => {
  return {
    x:
      currentPosition.x * (1 - frameTicksElapsed) +
      nextPosition.x * frameTicksElapsed,
    y:
      currentPosition.y * (1 - frameTicksElapsed) +
      nextPosition.y * frameTicksElapsed,
  };
};

export const setElementOpacity = (
  element,
  sequence,
  roundedFrame,
  hidden,
  frameTicksElapsed,
  animate,
) => {
  const currentFrame = sequence.frames[roundedFrame];
  const nextFrame = sequence.frames[roundedFrame + 1];

  if (animate) {
    // this is horrible, we need to fix that
    if (currentFrame && nextFrame) {
      const visibleNow = elementIsVisibleAtFrame(
        element.id,
        sequence.id,
        roundedFrame,
      );
      const visibleNext = elementIsVisibleAtFrame(
        element.id,
        sequence.id,
        roundedFrame + 1,
      );

      if (visibleNow && !visibleNext) {
        element.graphicElement
          .animate({ duration: 100, when: "now" })
          .opacity(
            frameTicksElapsed <= 1 - TIME_TO_CHANGE_OPACITY
              ? 1
              : 1 -
                  (frameTicksElapsed - (1 - TIME_TO_CHANGE_OPACITY)) /
                    TIME_TO_CHANGE_OPACITY,
          );
      } else if (!visibleNow && visibleNext) {
        element.graphicElement
          .animate({ duration: 100, when: "now" })
          .opacity(
            frameTicksElapsed <= 1 - TIME_TO_CHANGE_OPACITY
              ? hidden
              : (frameTicksElapsed - (1 - TIME_TO_CHANGE_OPACITY)) /
                  TIME_TO_CHANGE_OPACITY,
          );
      } else {
        element.graphicElement
          .animate({ duration: 100, when: "now" })
          .opacity(visibleNow ? 1 : hidden);
      }
    } else {
      element.graphicElement
        .animate({ duration: 100, when: "now" })
        .opacity(
          elementIsVisibleAtFrame(element.id, sequence.id, roundedFrame)
            ? 1
            : hidden,
        );
    }
  } else {
    if (currentFrame && nextFrame) {
      const visibleNow = elementIsVisibleAtFrame(
        element.id,
        sequence.id,
        roundedFrame,
      );
      const visibleNext = elementIsVisibleAtFrame(
        element.id,
        sequence.id,
        roundedFrame + 1,
      );

      if (visibleNow && !visibleNext) {
        element.graphicElement.opacity(
          frameTicksElapsed <= 1 - TIME_TO_CHANGE_OPACITY
            ? 1
            : 1 -
                (frameTicksElapsed - (1 - TIME_TO_CHANGE_OPACITY)) /
                  TIME_TO_CHANGE_OPACITY,
        );
      } else if (!visibleNow && visibleNext) {
        element.graphicElement.opacity(
          frameTicksElapsed <= 1 - TIME_TO_CHANGE_OPACITY
            ? hidden
            : (frameTicksElapsed - (1 - TIME_TO_CHANGE_OPACITY)) /
                TIME_TO_CHANGE_OPACITY,
        );
      } else {
        element.graphicElement.opacity(visibleNow ? 1 : hidden);
      }
    } else {
      element.graphicElement.opacity(
        elementIsVisibleAtFrame(element.id, sequence.id, roundedFrame)
          ? 1
          : hidden,
      );
    }
  }
};

export const setDisplayToggleFromCurrentVisibility = (sequence, elementId) => {
  if (
    sequence.currentFrame % 1 !== 0 ||
    store.state.diagramMode !== diagramMode.recordingMode
  ) {
    hideVisibilityToggles();
    return;
  }
  if (elementIsVisibleAtFrame(elementId, sequence.id, sequence.currentFrame)) {
    drawAdapter.showDisplayOffToggle(elementId);
  } else {
    drawAdapter.showDisplayOnToggle(elementId);
  }
};

function animateSegmentsVisibility(sequenceId, frame, playingSequence) {
  let roundedFrame = Math.floor(frame);
  const frameTicksElapsed = frame % 1;
  drawAdapter.parameters.allSegments.forEach((segment) => {
    const storedSegment = store.state.allSimpleSegments.get(segment.id);
    if (!storedSegment) {
      return;
    }
    const element1Id = storedSegment.segmentElementsInfo.element1;
    const element2Id = storedSegment.segmentElementsInfo.element2;
    const storeElement1 = store.state.allSimpleElements.get(element1Id);
    const storeElement2 = store.state.allSimpleElements.get(element2Id);
    if (
      storeElement1.sequences?.size === 0 ||
      storeElement2.sequences?.size === 0
    ) {
      return;
    }
    const element1DestinationDisplayNextFrame =
      storeElement1.sequences?.get(sequenceId)[
        roundedFrame + (playingSequence ? 1 : 0) + 1
      ]?.display;
    const element2DestinationDisplayNextFrame =
      storeElement2.sequences?.get(sequenceId)[
        roundedFrame + (playingSequence ? 1 : 0) + 1
      ]?.display;

    const hidden =
      store.state.diagramMode === diagramMode.recordingMode
        ? HIDDEN_OPACITY_IN_RECORDING
        : 0;

    // disappearing
    let element1Visible = elementIsVisibleAtFrame(
      element1Id,
      sequenceId,
      roundedFrame + (playingSequence ? 1 : 0),
    );
    let element2Visible = elementIsVisibleAtFrame(
      element2Id,
      sequenceId,
      roundedFrame + (playingSequence ? 1 : 0),
    );
    if (!element1Visible && !element2Visible) {
      segment.graphicSegment.opacity(hidden);
    } else if (element1Visible && !element2Visible) {
      if (element2DestinationDisplayNextFrame === "on") {
        segment.graphicSegment.opacity(
          (Math.min(frameTicksElapsed, TIME_TO_CHANGE_OPACITY) /
            TIME_TO_CHANGE_OPACITY) *
            (1 - hidden) +
            hidden,
        );
      } else {
        segment.graphicSegment.opacity(hidden);
      }
    } else if (!element1Visible && element2Visible) {
      if (element1DestinationDisplayNextFrame === "on") {
        segment.graphicSegment.opacity(
          (Math.min(frameTicksElapsed, TIME_TO_CHANGE_OPACITY) /
            TIME_TO_CHANGE_OPACITY) *
            (1 - hidden) +
            hidden,
        );
      } else {
        segment.graphicSegment.opacity(hidden);
      }
    } else if (element1Visible && element2Visible) {
      if (
        element1DestinationDisplayNextFrame === "off" ||
        element2DestinationDisplayNextFrame === "off"
      ) {
        segment.graphicSegment.opacity(
          (1 -
            Math.min(frameTicksElapsed, TIME_TO_CHANGE_OPACITY) /
              TIME_TO_CHANGE_OPACITY) *
            (1 - hidden) +
            hidden,
        );
      } else {
        segment.graphicSegment.opacity(1);
      }
    }
  });
}

const getResizedViewbox = (
  originalX,
  ooriginalY,
  originalWidth,
  originalHeight,
  windowWidthOriginal,
  windowHeightOriginal,
) => {
  const newWindowWidth = window.innerWidth;
  const scaleFactorX = (windowWidthOriginal || newWindowWidth) / newWindowWidth;
  const newSvgWidth = originalWidth * scaleFactorX;
  const newSvgHeight = originalHeight * scaleFactorX;

  // First we find the original centre of the SVG
  const svgYCentre =
    ooriginalY + (windowHeightOriginal * (originalHeight / 10000)) / 2;
  // Then we find the new Y position of the top-left corner of the SVG
  const newSvgY =
    svgYCentre - (window.innerHeight * (newSvgHeight / 10000)) / 2;

  if (
    originalX === undefined ||
    ooriginalY === undefined ||
    newSvgWidth === undefined ||
    newSvgHeight === undefined
  ) {
    return;
  }

  return {
    x: originalX,
    y: newSvgY,
    width: newSvgWidth,
    height: newSvgHeight,
  };
};

export const smallZoomOut = () => {
  const viewbox = draw.viewbox();
  const newX =
    viewbox.x -
    ((window.innerWidth * viewbox.width) / 10000 -
      (window.innerWidth * viewbox.width) / 15000) /
      1.5;
  const newY =
    viewbox.y -
    ((window.innerHeight * viewbox.height) / 10000 -
      (window.innerHeight * viewbox.height) / 15000) /
      1.5;
  draw
    .animate({ duration: 200, when: "now" })
    .viewbox(newX, newY, viewbox.width * 1.5, viewbox.height * 1.5)
    .after(() => resetViewBounds());
};

export const immersiveViewOpacity = (value) => {
  drawAdapter.parameters.immersiveViewbox.opacity(value);
};

const getRealTime = (processingUnitTime) => {
  return processingUnitTime * store.state.millisecondsPerProcessingUnit;
};

window.addEventListener("message", async (event) => {
  const { action } = event.data;

  let sequenceId1 = store.state.currentSequenceId;
  switch (action) {
    case "play":
      await playSequence(sequenceId1);
      break;
    case "pause":
      await pauseSequence(sequenceId1);
      break;
  }
});

export const zoomToElement = (elementId) => {
  const element = store.state.allSimpleElements.get(elementId);
  if (element) {
    draw.animate({ duration: 600, when: "now" }).ease("<").zoom(20, {
      x: element.workspaceParameters[store.state.currentWorkspaceId].x,
      y: element.workspaceParameters[store.state.currentWorkspaceId].y,
    });
  }
};
